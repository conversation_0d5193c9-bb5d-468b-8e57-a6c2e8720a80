import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { MissingComponent, MISSING_IDENT_TOKEN } from './missing.component';

const meta: Meta<MissingComponent> = {
  title: 'Ui Common/Domain Controls/MissingComponent',
  component: MissingComponent,
  decorators: [
    moduleMetadata({
      providers: [
        { provide: MISSING_IDENT_TOKEN, useValue: 'mock-control-xyz' },
      ],
    }),
  ],
};

export default meta;
type Story = StoryObj<MissingComponent>;

export const Default: Story = {};
