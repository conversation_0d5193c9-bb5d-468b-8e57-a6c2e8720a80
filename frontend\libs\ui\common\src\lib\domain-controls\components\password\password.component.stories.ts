import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { PasswordComponent } from './password.component';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ControlContextInjectionToken } from '../../model/control-context.interface';
import { IStringControlContext } from '../../model/string-control-context.interface';
import { DebounceChangeDirective } from '../../../directives/ng-model-debounce.directive';

// Create a mock control context
const createMockControlContext = (args: Partial<IStringControlContext<string>>): IStringControlContext<string> => ({
  fieldId: 'userPassword',
  value: '',
  maxLength: 20,
  placeholder: 'Enter your password',
  readonly: false,
  validationResults: [],
  entityContext: { totalCount: 0 },
  ...args
});

const meta: Meta<PasswordComponent & {
  maxLength?: number;
  placeholder?: string;
  value?: string;
  readonly?: boolean;
}> = {
  title: 'UI Common/Domain Controls/Password',
  component: PasswordComponent,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component: `
A password input component that provides secure text input with:
- Hidden text display for security
- Customizable placeholder text
- Character length limits via maxLength
- Readonly mode support
- Proper form integration with ngModel

**Key Features:**
- Secure password input with masked characters
- Supports maxLength attribute for password policies
- Fully accessible with proper form controls
- Integrates with Angular reactive forms
        `,
      },
    },
  },
  decorators: [
    moduleMetadata({
      declarations: [],
      imports: [CommonModule, FormsModule,DebounceChangeDirective],
    }),
  ],
  argTypes: {
    maxLength: {
      control: { type: 'number', min: 6, max: 128, step: 1 },
      description: 'Maximum number of characters allowed for the password',
    },
    placeholder: {
      control: { type: 'text' },
      description: 'Placeholder text shown when input is empty',
    },
    value: {
      control: { type: 'text' },
      description: 'Current value of the password field',
    },
    readonly: {
      control: { type: 'boolean' },
      description: 'Whether the input is readonly',
    },
  },
  render: (args: { maxLength?: number; placeholder?: string; value?: string; readonly?: boolean }) => {
    const controlContext = createMockControlContext({
      maxLength: args.maxLength,
      placeholder: args.placeholder,
      value: args.value,
      readonly: args.readonly,
    });

    return {
      props: {},
      moduleMetadata: {
        providers: [
          { provide: ControlContextInjectionToken, useValue: controlContext }
        ]
      }
    };
  },
};

export default meta;
type Story = StoryObj<PasswordComponent & {
  maxLength?: number;
  placeholder?: string;
  value?: string;
  readonly?: boolean;
}>;

export const Default: Story = {
  args: {
    maxLength: 20,
    placeholder: 'Enter your password',
    value: '',
    readonly: false,
  },
};
