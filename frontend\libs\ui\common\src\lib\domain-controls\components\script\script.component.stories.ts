
import { <PERSON>a, StoryObj, moduleMetadata } from '@storybook/angular';
import { ScriptComponent } from './script.component';
import { CommonModule } from '@angular/common';
import { ControlContextInjectionToken } from '../../model/control-context.interface';
import { IScriptControlContext } from '../../model/script-control-context.interface';
import { CodemirrorLanguageModes } from '../../../model/script/codemirror-language-modes.enum';

// Sample code content for different languages
const sampleContent: Record<CodemirrorLanguageModes, string> = {
  [CodemirrorLanguageModes.JavaScript]: `// JavaScript Example
function greetUser(name) {
  console.log(\`Hello, \${name}!\`);
  return \`Welcome to the script editor, \${name}!\`;
}

// Call the function
const message = greetUser('Developer');
document.getElementById('output').innerHTML = message;`,

  [CodemirrorLanguageModes.Json]: `{
  "name": "Script Editor",
  "version": "1.0.0",
  "description": "A powerful script editor component",
  "features": [
    "Syntax highlighting",
    "Line numbers",
    "Auto-completion",
    "Multiple language support"
  ],
  "config": {
    "theme": "default",
    "multiline": true,
    "readOnly": false
  }
}`,

  [CodemirrorLanguageModes.Xml]: `<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <editor>
    <language>XML</language>
    <features>
      <syntax-highlighting enabled="true"/>
      <line-numbers enabled="true"/>
      <auto-completion enabled="true"/>
    </features>
  </editor>
  <settings>
    <multiline>true</multiline>
    <read-only>false</read-only>
    <border enabled="true"/>
  </settings>
</configuration>`,

  [CodemirrorLanguageModes.Html]: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Script Editor Demo</title>
</head>
<body>
  <h1>Welcome to Script Editor</h1>
  <div id="editor-container">
    <p>This is a sample HTML content for testing the script editor component.</p>
  </div>
</body>
</html>`,

  [CodemirrorLanguageModes.MsSQL]: `-- SQL Server Example
SELECT 
  u.UserId,
  u.UserName,
  u.Email,
  COUNT(o.OrderId) as TotalOrders
FROM Users u
LEFT JOIN Orders o ON u.UserId = o.UserId
WHERE u.CreatedDate >= '2023-01-01'
GROUP BY u.UserId, u.UserName, u.Email
ORDER BY TotalOrders DESC;`
};

// Define the story arguments type
type StoryArgs = {
  fieldId?: string;
  value?: string;
  languageMode?: CodemirrorLanguageModes;
  enableLineNumbers?: boolean;
  enableBorder?: boolean;
  multiline?: boolean;
  readOnly?: boolean;
  isInputOutput?: boolean;
  usePresetContent?: boolean;
  readonly?: boolean;
};

const meta: Meta<StoryArgs> = {
  title: 'UI Common/Domain Controls/Script',
  component: ScriptComponent,
  parameters: {
    docs: {
      description: {
        component: `
A script editor component built with CodeMirror that provides syntax highlighting and editing capabilities.

**Key Features:**
- Multiple language support (JavaScript, JSON, XML, HTML, SQL)
- Syntax highlighting and code formatting
- Line numbers and code folding
- Auto-completion and bracket matching
- Configurable read-only mode
- Multiline and single-line editing modes
- Customizable borders and styling
- Input/Output mode for larger content areas

**Language Modes:**
- **JavaScript**: Full JavaScript syntax support
- **JSON**: JSON formatting and validation
- **XML**: XML structure highlighting
- **HTML**: HTML tag and attribute highlighting
- **MsSQL**: SQL Server query syntax

**Editor Options:**
- Enable/disable line numbers
- Toggle read-only mode
- Multiline vs single-line editing
- Custom border styling
- Input/Output mode for larger text areas
        `,
      },
    },
  },
  argTypes: {
    // Field Configuration
    fieldId: {
      control: { type: 'text' },
      description: 'Unique identifier for the script field',
    },
    value: {
      control: { type: 'text' },
      description: 'Initial script content',
    },
    
    // Editor Configuration
    languageMode: {
      control: { type: 'select' },
      options: [
        CodemirrorLanguageModes.JavaScript,
        CodemirrorLanguageModes.Json,
        CodemirrorLanguageModes.Xml,
        CodemirrorLanguageModes.Html,
        CodemirrorLanguageModes.MsSQL
      ],
      mapping: {
        'JavaScript': CodemirrorLanguageModes.JavaScript,
        'JSON': CodemirrorLanguageModes.Json,
        'XML': CodemirrorLanguageModes.Xml,
        'HTML': CodemirrorLanguageModes.Html,
        'MS SQL': CodemirrorLanguageModes.MsSQL
      },
      description: 'Programming language mode for syntax highlighting',
    },
    
    // Editor Features
    enableLineNumbers: {
      control: { type: 'boolean' },
      description: 'Show line numbers in the editor',
    },
    enableBorder: {
      control: { type: 'boolean' },
      description: 'Show border around the editor',
    },
    multiline: {
      control: { type: 'boolean' },
      description: 'Enable multiline editing mode',
    },
    readOnly: {
      control: { type: 'boolean' },
      description: 'Make the editor read-only',
    },
    isInputOutput: {
      control: { type: 'boolean' },
      description: 'Enable input/output mode with larger minimum height',
    },
    
    // Content Presets
    usePresetContent: {
      control: { type: 'boolean' },
      description: 'Use sample content for the selected language',
    },
    
    // State Configuration
    readonly: {
      control: { type: 'boolean' },
      description: 'Whether the field is readonly (control context level)',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [CommonModule],
    }),
  ],
  render: (args: StoryArgs) => {
    // Determine content based on args
    let content = args.value || '';
    if (args.usePresetContent && args.languageMode !== undefined) {
      // Safely access sample content with type checking
      const languageMode = args.languageMode as CodemirrorLanguageModes;
      content = sampleContent[languageMode] || content;
    }

    // Create dynamic control context based on args
    const controlContext: IScriptControlContext = {
      fieldId: args.fieldId || 'scriptField',
      value: content,
      readonly: args.readonly ?? false,
      validationResults: [],
      entityContext: { totalCount: 0 },
      editorOptions: {
        languageMode: args.languageMode ?? CodemirrorLanguageModes.JavaScript,
        enableLineNumbers: args.enableLineNumbers ?? true,
        enableBorder: args.enableBorder ?? true,
        multiline: args.multiline ?? true,
        readOnly: args.readOnly ?? false,
        isInputOutput: args.isInputOutput ?? false,
      }
    };

    return {
      template: '<ui-common-script></ui-common-script>',
      moduleMetadata: {
        providers: [
          { provide: ControlContextInjectionToken, useValue: controlContext },
        ]
      }
    };
  },
};

export default meta;

type Story = StoryObj<StoryArgs>;

export const Default: Story = {
  args: {
    fieldId: 'scriptField',
    value: 'console.log("Hello, world!");',
    languageMode: CodemirrorLanguageModes.JavaScript,
    enableLineNumbers: true,
    enableBorder: true,
    multiline: true,
    readOnly: false,
    isInputOutput: false,
    usePresetContent: false,
    readonly: false,
  },
};

export const JavaScriptWithPreset: Story = {
  args: {
    fieldId: 'jsScriptField',
    languageMode: CodemirrorLanguageModes.JavaScript,
    enableLineNumbers: true,
    enableBorder: true,
    multiline: true,
    readOnly: false,
    isInputOutput: false,
    usePresetContent: true,
    readonly: false,
  },
};

export const JsonEditor: Story = {
  args: {
    fieldId: 'jsonField',
    languageMode: CodemirrorLanguageModes.Json,
    enableLineNumbers: true,
    enableBorder: true,
    multiline: true,
    readOnly: false,
    isInputOutput: false,
    usePresetContent: true,
    readonly: false,
  },
};

export const SqlQuery: Story = {
  args: {
    fieldId: 'sqlField',
    languageMode: CodemirrorLanguageModes.MsSQL,
    enableLineNumbers: true,
    enableBorder: true,
    multiline: true,
    readOnly: false,
    isInputOutput: true,
    usePresetContent: true,
    readonly: false,
  },
};

export const ReadOnlyXml: Story = {
  args: {
    fieldId: 'xmlField',
    languageMode: CodemirrorLanguageModes.Xml,
    enableLineNumbers: true,
    enableBorder: true,
    multiline: true,
    readOnly: true,
    isInputOutput: false,
    usePresetContent: true,
    readonly: false,
  },
};

export const HtmlEditor: Story = {
  args: {
    fieldId: 'htmlField',
    languageMode: CodemirrorLanguageModes.Html,
    enableLineNumbers: true,
    enableBorder: true,
    multiline: true,
    readOnly: false,
    isInputOutput: true,
    usePresetContent: true,
    readonly: false,
  },
};

export const MinimalEditor: Story = {
  args: {
    fieldId: 'minimalField',
    value: 'Simple text content',
    languageMode: CodemirrorLanguageModes.JavaScript,
    enableLineNumbers: false,
    enableBorder: false,
    multiline: false,
    readOnly: false,
    isInputOutput: false,
    usePresetContent: false,
    readonly: false,
  },
};
