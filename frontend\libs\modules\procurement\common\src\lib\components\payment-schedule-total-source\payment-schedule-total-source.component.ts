/*
 * Copyright(c) RIB Software GmbH
 */

import {
	Input,
	inject,
	Output,
	Component,
	OnDestroy,
	EventEmitter
} from '@angular/core';
import * as math from 'mathjs';
import { isNil } from 'lodash';
import { firstValueFrom } from 'rxjs';
import { IEntityBase, IEntityContext } from '@libs/platform/common';
import { BasicsSharedCalculateOverGrossService } from '@libs/basics/shared';
import { ProcurementCommonCalculationService } from '../../services/helper/procurement-common-calculation.service';
import { PrcCommonPaymentScheduleTotalSourceLookupService } from '../../lookups/payment-schedule-total-source-lookup.service';
import { IPrcCommonPaymentScheduleTotalSourceEntity } from '../../model/entities/prc-payment-schedule-total-source-entity.interface';
import { IPrcCommonPaymentScheduleTotalSourceContextEntity } from '../../model/entities/prc-payment-schedule-total-source-entity.interface';
import { PrcSharedTotalTypeLookupService } from '@libs/procurement/shared';
import { ProcurementTotalKind } from '../../model/enums/procurement-total-kind.enum';
import { FieldType } from '@libs/ui/common';

/**
 * Procurement common payment schedule total source composite component
 */
@Component({
	selector: 'procurement-common-payment-schedule-total-source',
	templateUrl: './payment-schedule-total-source.component.html',
	styleUrl: './payment-schedule-total-source.component.scss'
})
export class ProcurementCommonPaymentScheduleTotalSourceComponent implements OnDestroy {
	private readonly calculationService = inject(ProcurementCommonCalculationService);
	private readonly totalTypeLookupService = inject(PrcSharedTotalTypeLookupService);
	public readonly totalSourceLookupService = inject(PrcCommonPaymentScheduleTotalSourceLookupService);
	public readonly isOverGross = inject(BasicsSharedCalculateOverGrossService).isOverGross;
	public context: IEntityContext<IPrcCommonPaymentScheduleTotalSourceContextEntity> = {totalCount: 0};
	public sourceId: number | null = null;
	public sourceNetOc: number = 0;
	public sourceGrossOc: number = 0;

	/**
	 * Is readonly
	 */
	@Input()
	public readonly: boolean = true;

	/**
	 * Total source lookup context entity
	 */
	@Input()
	public set contextEntity(newContext: IPrcCommonPaymentScheduleTotalSourceContextEntity) {
		this.onContextEntityChange(newContext);
	}

	/**
	 * Updated parent
	 */
	@Input()
	public set headerUpdate(parent: (IEntityBase & { Id: number }) | null) {
		this.onHeaderUpdate(parent?.Id);
	}

	/**
	 * Source change emitter
	 */
	@Output()
	public sourceChanged = new EventEmitter<IPrcCommonPaymentScheduleTotalSourceContextEntity>();

	private get contextVatPercent(): number {
		return this.context.entity?.VatPercent ?? 0;
	}

	private get contextSourceNetOc(): number {
		return this.context.entity?.SourceNetOc ?? 0;
	}

	private get contextSourceGrossOc(): number {
		return this.context.entity?.SourceGrossOc ?? 0;
	}

	/**
	 * FieldType Money
	 */
	public get moneyFieldType() {
		return FieldType.Money;
	}

	private async onContextEntityChange(newContextEntity: IPrcCommonPaymentScheduleTotalSourceContextEntity) {
		this.totalSourceLookupService.clearCache();
		if (newContextEntity.ParentId) {
			this.updateContext({
				ParentId: newContextEntity.ParentId,
				VatPercent: newContextEntity.VatPercent,
				SourceNetOc: this.sourceNetOc,
				SourceGrossOc: this.sourceGrossOc,
				ModuleName: newContextEntity.ModuleName
			});
			await this.loadTotalSourceList();
			if (!newContextEntity.SourceGrossOc && !newContextEntity.SourceNetOc) {
				await this.updateSourceValueBySourceId(newContextEntity.SourceId ?? this.totalSourceLookupService.totalSourceIdOfMainNChangeOrder);
			} else {
				this.sourceNetOc = newContextEntity.SourceNetOc;
				this.sourceGrossOc = newContextEntity.SourceGrossOc;
			}
		}
	}

	private updateContext(contextEntity: IPrcCommonPaymentScheduleTotalSourceContextEntity) {
		this.context = {
			entity: contextEntity,
			totalCount: 0
		};
	}

	private async updateSourceValueBySourceId(id: number) {
		let source = await this.totalSourceLookupService.getItemById(id, this.context);
		if (!source) {
			source = await this.getNetTotalSource();
		}

		if (source) {
			this.sourceId = source.Id;
			this.updateSourceValueBySource(source);
			this.onTotalSourceValueChange();
		}
	}

	/**
	 * Get net total source
	 * @private
	 */
	private async getNetTotalSource() {
		const sources = this.totalSourceLookupService.getListSync();
		const typeIds = sources.map(e => e.TypeId);
		const types = await this.totalTypeLookupService.getListAsync();
		const netType = types.find(t => t.PrcTotalKindFk === ProcurementTotalKind.NetTotal && typeIds.includes(t.Id));
		return netType ? sources.find(s => s.TypeId = netType.Id) : null;
	}

	private async loadTotalSourceList() {
		await firstValueFrom(this.totalSourceLookupService.getList(this.context));
	}

	/**
	 * On total source lookup value changed
	 * @param sourceId
	 */
	public validateSourceId(sourceId: number) {
		const source = this.totalSourceLookupService.getListSync().find(s => s.Id === sourceId);
		if (source) {
			this.updateSourceValueBySource(source);
			this.onTotalSourceValueChange();
		}
	}

	/**
	 * On Total source netOc changed
	 */
	public validateTotalSourceNetOc = this.validateTotalSourceValue('TotalNetOc');

	/**
	 * On Total source grossOc changed
	 */
	public validateTotalSourceGrossOc = this.validateTotalSourceValue('TotalGrossOc');

	private validateTotalSourceValue(field: 'TotalNetOc' | 'TotalGrossOc') {
		return () => {
			const isNetValue = field === 'TotalNetOc';
			const value = (isNetValue ? this.sourceNetOc : this.sourceGrossOc) ?? 0;
			const oldValue = (isNetValue ? this.contextSourceNetOc : this.contextSourceGrossOc) ?? 0;
			if (value === oldValue) {
				return;
			}

			const sources = this.totalSourceLookupService.getListSync();
			const source = isNetValue ?
				sources.find(s => s.ValueNetOc === value) :
				sources.find(s => s.ValueGrossOc === value);
			if (source) {
				this.sourceId = source.Id;
				this.updateSourceValueBySource(source);
			} else {
				this.sourceId = null;
				this.sourceNetOc = isNetValue ? math.bignumber(value).toNumber() : this.calculationService.getPreTaxValueByAfterTaxValue(value, this.contextVatPercent);
				this.sourceGrossOc = !isNetValue ? math.bignumber(value).toNumber() : this.calculationService.getAfterTaxValueByPreTaxValue(value, this.contextVatPercent);
			}
			this.onTotalSourceValueChange();
		};
	}

	private updateSourceValueBySource(source: IPrcCommonPaymentScheduleTotalSourceEntity) {
		this.sourceNetOc = source.ValueNetOc;
		this.sourceGrossOc = !isNil(source.ValueGrossOc) ? source.ValueGrossOc : math.round(math.bignumber(source.ValueNetOc).add(source.ValueTaxOc), 2).toNumber();
	}

	private onTotalSourceValueChange() {
		if (this.context.entity) {
			this.context.entity.SourceNetOc = this.sourceNetOc;
			this.context.entity.SourceGrossOc = this.sourceGrossOc;
			this.context.entity.SourceId = this.sourceId;
		}
		this.sourceChanged.emit(this.context.entity);
	}

	/**
	 * Handle on header updated
	 * @param parentId
	 * @private
	 */
	private async onHeaderUpdate(parentId?: number | null) {
		if (!parentId) {
			return;
		}

		await this.totalSourceLookupService.reloadList(parentId, this.context?.entity?.ModuleName);
		if (this.sourceId) {
			const originalNetOc = this.sourceNetOc;
			const originalGrossOc = this.sourceGrossOc;
			const source = this.totalSourceLookupService.getListSync().find(s => s.Id === this.sourceId);
			if (source) {
				this.updateSourceValueBySource(source);
				if (originalNetOc !== this.sourceNetOc || originalGrossOc !== this.sourceGrossOc) {
					this.onTotalSourceValueChange();
				}
			}
		}
	}

	/**
	 * Handle on component destroy
	 */
	public ngOnDestroy() {
		this.totalSourceLookupService.clearCache();
	}
}