/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IntegerComponent } from './integer.component';
import { DebounceChangeDirective } from '../../../directives/ng-model-debounce.directive';
import { INumericControlContext } from '../../model/numeric-control-context.interface';
import { ControlContextInjectionToken } from '../../model/control-context.interface';
import { UiNumericConverterService } from '../../services/numeric-converter.service';
import { MinimalEntityContext, IEntityContext, PlatformConfigurationService, TranslatePipe, PlatformTranslateService, PlatformLanguageService } from '@libs/platform/common';
import { HttpClientModule } from '@angular/common/http';

// Create a mock control context
const createMockControlContext = (args: Partial<INumericControlContext>): INumericControlContext => ({
	fieldId: 'integerValue',
	readonly: false,
	validationResults: [],
	value: 0,
	minValue: undefined,
	maxValue: undefined,
	placeholder: 'Enter integer...',
	get entityContext(): IEntityContext<object> {
		return new MinimalEntityContext<object>();
	},
	...args
});

export default {
	title: 'UI Common/Domain Controls/IntegerComponent',
	component: IntegerComponent,
	parameters: {
		docs: {
			description: {
				component: `
An integer input component that provides:
- Numeric input with integer validation
- Configurable min/max value constraints
- Customizable placeholder text
- Readonly mode support
- Automatic decimal removal
- Proper numeric formatting based on locale

**Key Features:**
- Restricts input to integers only (no decimals)
- Supports minValue and maxValue for validation
- Locale-aware number formatting
- Integrates with Angular reactive forms
- Built-in numeric conversion and validation
			`,
			},
		},
	},
	decorators: [
		moduleMetadata({
			imports: [FormsModule, DebounceChangeDirective, HttpClientModule,TranslatePipe],
			declarations: [],
			providers: [
				UiNumericConverterService,
				PlatformConfigurationService,
				PlatformTranslateService,
				PlatformLanguageService,
				
			],
		}),
	],
	argTypes: {
		minValue: {
			control: { type: 'number', step: 1 },
			description: 'Minimum allowable integer value',
		},
		maxValue: {
			control: { type: 'number', step: 1 },
			description: 'Maximum allowable integer value',
		},
		placeholder: {
			control: { type: 'text' },
			description: 'Placeholder text shown when input is empty',
		},
		value: {
			control: { type: 'number', step: 1 },
			description: 'Current integer value',
		},
		readonly: {
			control: { type: 'boolean' },
			description: 'Whether the input is readonly',
		},
	},
	render: (args: { minValue?: number; maxValue?: number; placeholder?: string; value?: number; readonly?: boolean }) => {
		const controlContext = createMockControlContext({
			minValue: args.minValue,
			maxValue: args.maxValue,
			placeholder: args.placeholder,
			value: args.value,
			readonly: args.readonly,
		});

		return {
			props: {},
			moduleMetadata: {
				providers: [
					{ provide: ControlContextInjectionToken, useValue: controlContext }
				]
			}
		};
	},
} as Meta<IntegerComponent & {
	minValue?: number;
	maxValue?: number;
	placeholder?: string;
	value?: number;
	readonly?: boolean;
}>;

type Story = StoryObj<IntegerComponent & {
	minValue?: number;
	maxValue?: number;
	placeholder?: string;
	value?: number;
	readonly?: boolean;
}>;

export const Default: Story = {
	args: {
		minValue: undefined,
		maxValue: undefined,
		placeholder: 'Enter integer...',
		readonly: false,
	},
};

