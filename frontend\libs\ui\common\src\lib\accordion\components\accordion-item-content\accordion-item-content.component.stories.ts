import { <PERSON>a, StoryObj, moduleMetadata } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { UiCommonAccordionItemContentComponent } from './accordion-item-content.component';
import { AccordionContext } from '../../model/accordion-context';
import { IAccordionItem } from '../../model/interfaces/accordion-item.interface';
import { PlatformConfigurationService, PlatformTranslateService, TranslatePipe } from '@libs/platform/common';
import { HttpClientModule } from '@angular/common/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { IAccordionConfig } from '../../model/interfaces/accordion-config.interface';


const mockAccordionItem: IAccordionItem = {
  id: 182,
  title: { text: 'Project Overview' },
  comment: { text: 'groupable overview of all projects' },
  actionButtons: [],
  providers: [],
  imgCss: 'ico-report01',
  disabled: false,
};

// Provide a mock config object as the second argument
const mockAccordionConfig = {} as IAccordionConfig; 
const mockAccordionContext = new AccordionContext(mockAccordionItem, mockAccordionConfig);

export default {
  title: 'UI Common/Accordion/AccordionItemContent',
  component: UiCommonAccordionItemContentComponent,
  decorators: [
    moduleMetadata({
      imports: [CommonModule,HttpClientModule,BrowserAnimationsModule, TranslatePipe],
      declarations: [UiCommonAccordionItemContentComponent],
      providers: [
        PlatformTranslateService,
        PlatformConfigurationService,
        { provide: AccordionContext, useValue: mockAccordionContext }
      ]
    }),
  ],
  render: (args) => ({
    props: {
      ...args,
      context: mockAccordionContext
    },
  }),
} as Meta<UiCommonAccordionItemContentComponent>;

type Story = StoryObj<UiCommonAccordionItemContentComponent>;

export const Default: Story = {
  args: {},
};