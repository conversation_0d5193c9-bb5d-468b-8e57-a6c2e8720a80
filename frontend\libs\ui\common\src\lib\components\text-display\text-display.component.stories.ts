import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { TextDisplayComponent } from './text-display.component';
import { TextDisplayType } from '../../model/text-display/enums/text-display-type.enum';

export default {
  title: 'UI Common/Components/TextDisplay/TextDisplayComponent',
  component: TextDisplayComponent,
  parameters: {
    docs: {
      description: {
        component: `The <code style="background-color: #f0f0f0; color: #d6336c;">TextDisplayComponent</code> is used to display text in multiple formats:<br/>
- <b style="color: green;">HTML</b> using <code>TextDisplayType.Html</code><br/>
- <span style="color: #007bff;">Plain Text</span> using <code>TextDisplayType.Plain</code><br/>
- <span style="background-color: yellow;">Code</span> using <code>TextDisplayType.Code</code><br/><br/> `,
      },
    },
  },
  decorators: [
    moduleMetadata({
      imports: [CommonModule],
      declarations: [TextDisplayComponent],
    }),
  ],
  argTypes: {
    type: {
      control: { type: 'select' },
      options: [TextDisplayType.Html, TextDisplayType.Code, TextDisplayType.Plain],
      description: 'Display type: html, code, or plain',
      defaultValue: TextDisplayType.Plain,
    },
    text: {
      control: 'text',
      description: 'Text to display',
      defaultValue: 'This is plain text. No formatting will be applied.',
    },
  },
  render: (args) => ({
    props: {
      ...args,
      DisplayType: TextDisplayType,
    },
  }),
} as Meta<TextDisplayComponent>;

type Story = StoryObj<TextDisplayComponent>;

export const Playground: Story = {
  args: {
    type: TextDisplayType.Plain,
    text: 'This is plain text. No formatting will be applied.',
  },
  parameters: {
    docs: {
      description: {
        story: 'Use the controls to change the display type and text live.',
      },
    },
  },
};

