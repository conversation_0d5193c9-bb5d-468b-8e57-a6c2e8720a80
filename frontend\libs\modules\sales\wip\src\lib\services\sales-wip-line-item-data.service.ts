/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import { DataServiceFlatLeaf, IDataServiceChildRoleOptions, IDataServiceEndPointOptions, IDataServiceOptions, ServiceRole } from '@libs/platform/data-access';
import { WipHeaderComplete } from '../model/complete-class/wip-header-complete.class';
import { SalesWipWipsDataService } from './sales-wip-wips-data.service';
import { SalesWipLineItemModel } from '../model/sales-wip-line-item.model';
import { ISalesWipEstLineItemEntity, IWipHeaderEntity } from '@libs/sales/interfaces';

@Injectable({
	providedIn: 'root'
})
export class SalesWipLineItemDataService extends DataServiceFlatLeaf<ISalesWipEstLineItemEntity, IWipHeaderEntity, WipHeaderComplete> {

	public constructor(salesWipWipsDataService: SalesWipWipsDataService) {
		const options: IDataServiceOptions<ISalesWipEstLineItemEntity> = {
			apiUrl: 'estimate/main/lineitem',
			readInfo: <IDataServiceEndPointOptions> {
				endPoint: 'filterlineitems4sales',
				usePost: true,
			},
			deleteInfo: <IDataServiceEndPointOptions> {
				endPoint: 'multidelete'
			},
			roleInfo: <IDataServiceChildRoleOptions<ISalesWipEstLineItemEntity, IWipHeaderEntity, WipHeaderComplete>> {
				role: ServiceRole.Leaf,
				itemName: 'EstLineItem',
				parent: salesWipWipsDataService
			}
		};
		super(options);
	}
	protected override provideLoadPayload(): object {
		const parent = this.getSelectedParent();
		if (parent) {
			return {
				date: '2024-11-27T00:00:00.000Z',
				filter: '',
				isGeneral: true,
				projectFk: parent.ProjectFk,
				salesHeaderFk: parent.OrdHeaderFk,
				salesModule: 'wip',
			};
		}
		return {
			wipfk: -1,
		};
	}
	protected override onLoadSucceeded(loaded: SalesWipLineItemModel): ISalesWipEstLineItemEntity[] {
		return loaded.Main;
	}
}
