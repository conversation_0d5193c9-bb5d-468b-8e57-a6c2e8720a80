import { <PERSON>a, StoryObj, moduleMetadata } from '@storybook/angular';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { RadioComponent } from './radio.component';
import { TranslatePipe, PlatformTranslateService, PlatformLanguageService, PlatformConfigurationService, MinimalEntityContext, IEntityContext } from '@libs/platform/common';
import { ControlContextInjectionToken } from '../../model/control-context.interface';
import { ISelectControlContext } from '../../model/select-control-context.interface';
// import { ISelectItem, ISelectItemsSource } from '../../model/fields/additional/select-items-source.model';
import { HttpClientModule } from '@angular/common/http';
import { ISelectItem } from '../../../model/fields';

// Create a mock control context
const createMockControlContext = (args: {
	value?: string | number | boolean;
	readonly?: boolean;
	fieldId?: string;
	items?: ISelectItem<string | number | boolean>[];
}): ISelectControlContext => ({
	fieldId: args.fieldId || 'radioField',
	readonly: args.readonly || false,
	validationResults: [],
	value: args.value,
	itemsSource: {
		items: args.items || [
			{ id: '1', displayName: 'Option 1', iconCSS: 'icon-option1' },
			{ id: '2', displayName: 'Option 2', iconCSS: 'icon-option2' },
			{ id: '3', displayName: 'Option 3', iconCSS: 'icon-option3' },
		]
	},
	get entityContext(): IEntityContext<object> {
		return new MinimalEntityContext<object>();
	}
});

export default {
	title: 'UI Common/Domain Controls/RadioComponent',
	component: RadioComponent,
	parameters: {
		docs: {
			description: {
				component: `
A radio button group component that provides:
- Single selection from multiple options
- Configurable radio button items with icons
- Customizable field identification
- Readonly mode support
- Built-in validation support

**Key Features:**
- Support for multiple radio button options
- Each option can have custom display text and icon
- Single selection behavior (mutual exclusivity)
- Readonly mode for display-only scenarios
- Integrates with Angular reactive forms
- Validation result display support
			`,
			},
		},
	},
	decorators: [
		moduleMetadata({
			imports: [CommonModule, FormsModule, HttpClientModule,TranslatePipe],
			declarations: [],
			providers: [
				PlatformTranslateService,
				PlatformLanguageService,
				PlatformConfigurationService,
			],
		}),
	],
	argTypes: {
		value: {
			control: { type: 'select' },
			options: ['1', '2', '3', 'red', 'green', 'blue'],
			description: 'Currently selected radio button value',
		},
		readonly: {
			control: { type: 'boolean' },
			description: 'Whether the radio buttons are readonly/disabled',
		},
		fieldId: {
			control: { type: 'text' },
			description: 'Unique field identifier for the radio group',
		},
		optionType: {
			control: { type: 'select' },
			options: ['numbers', 'colors', 'sizes', 'priorities'],
			description: 'Type of options to display',
		},
	},
	render: (args: { 
		value?: string | number | boolean;
		readonly?: boolean;
		fieldId?: string;
		optionType?: string;
	}) => {
		let items: ISelectItem<string | number | boolean>[] = [];
		
		switch (args.optionType) {
			case 'colors':
				items = [
					{ id: 'red', displayName: 'Red', iconCSS: 'icon-circle text-red' },
					{ id: 'green', displayName: 'Green', iconCSS: 'icon-circle text-green' },
					{ id: 'blue', displayName: 'Blue', iconCSS: 'icon-circle text-blue' },
				];
				break;
			case 'sizes':
				items = [
					{ id: 'small', displayName: 'Small', iconCSS: 'icon-size-small' },
					{ id: 'medium', displayName: 'Medium', iconCSS: 'icon-size-medium' },
					{ id: 'large', displayName: 'Large', iconCSS: 'icon-size-large' },
				];
				break;
			case 'priorities':
				items = [
					{ id: 'low', displayName: 'Low Priority', iconCSS: 'icon-priority-low' },
					{ id: 'medium', displayName: 'Medium Priority', iconCSS: 'icon-priority-medium' },
					{ id: 'high', displayName: 'High Priority', iconCSS: 'icon-priority-high' },
				];
				break;
			default: // 'numbers'
				items = [
					{ id: '1', displayName: 'Option 1', iconCSS: 'icon-option1' },
					{ id: '2', displayName: 'Option 2', iconCSS: 'icon-option2' },
					{ id: '3', displayName: 'Option 3', iconCSS: 'icon-option3' },
				];
		}

		const controlContext = createMockControlContext({
			value: args.value,
			readonly: args.readonly,
			fieldId: args.fieldId,
			items: items
		});

		return {
			props: {},
			moduleMetadata: {
				providers: [
					{ provide: ControlContextInjectionToken, useValue: controlContext }
				]
			}
		};
	},
} as Meta<RadioComponent & {
	value?: string | number | boolean;
	readonly?: boolean;
	fieldId?: string;
	optionType?: string;
}>;

type Story = StoryObj<RadioComponent & {
	value?: string | number | boolean;
	readonly?: boolean;
	fieldId?: string;
	optionType?: string;
}>;

export const Default: Story = {
	args: {
		value: '1',
		readonly: false,
		fieldId: 'radioField',
		optionType: 'numbers',
	},
};

