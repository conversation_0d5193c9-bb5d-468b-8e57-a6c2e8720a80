import { Meta, moduleMetadata, Story } from '@storybook/angular';
import { FormsModule } from '@angular/forms';
import { InputSelectComponent } from './input-select.component';
import { InputSelectPopupComponent } from '../input-select-popup/input-select-popup.component';
import { PlatformLanguageService } from '@libs/platform/common';
import { ControlContextInjectionToken } from '../../model/control-context.interface';
import { IInputSelectControlContext } from '../../model/input-select-control-context.interface';
import { PopupService } from '../../../popup/services/popup.service';
import { HttpClientModule } from '@angular/common/http';
import { ActivePopup, UiCommonPopupComponent, UiCommonPopupContainerComponent } from '../../../popup';
import { Subject } from 'rxjs';
import { UiCommonPopupResizableDirective } from '../../../popup/directives/popup-resizable.directive';


const mockControlContext: IInputSelectControlContext = {
  fieldId: 'inputSelectField',
  readonly: false,
  validationResults: [],
  value: '',
  options: {
    items: [
      { id: '1', description: 'Estimate' },
      { id: '2', description: 'BoQ' },
      { id: '3', description: 'TimeKeeping' },
      { id: '4', description: 'Sales' },
      { id: '5', description: 'PPS' },
      { id: '6', description: 'Construction System' },
      { id: '7', description: 'Workflow' },
    ],
  },
  entityContext: { totalCount: 0 },
};

const activePopup = new Subject<ActivePopup>;
export default {
  title: 'UI Common/Domain Controls/InputSelectComponent',
  component: InputSelectComponent,
  decorators: [
    moduleMetadata({
      imports: [FormsModule, HttpClientModule,UiCommonPopupContainerComponent,UiCommonPopupComponent,UiCommonPopupResizableDirective],
      declarations: [ InputSelectPopupComponent],
      providers: [
        { provide: ControlContextInjectionToken, useValue: mockControlContext },
        { provide: 'activePopup', useValue: activePopup },
        PlatformLanguageService,
        PopupService
      ],
    }),
  ],
} as Meta<InputSelectComponent>;

const Template: Story<InputSelectComponent> = (args: InputSelectComponent) => ({
  props: args,
});

export const Default = Template.bind({});
Default.args = {
  controlContext: mockControlContext,
};

// export const DropdownOpensOnClick = Template.bind({});
// DropdownOpensOnClick.args = {
//   controlContext: mockControlContext,
// };
// DropdownOpensOnClick.play = async ({ canvasElement }) => {
//   const button = canvasElement.querySelector('button') as HTMLButtonElement;
//   button.click();
//   console.log('Dropdown opened');
// };

// export const UpdateSelectedValue = Template.bind({});
// UpdateSelectedValue.args = {
//   controlContext: mockControlContext,
// };
// UpdateSelectedValue.play = async ({ canvasElement }) => {
//   const input = canvasElement.querySelector('input') as HTMLInputElement;
//   input.value = 'Option 2';
//   input.dispatchEvent(new Event('input'));
//   console.log('Selected value updated to Option 2');
// };

// export const KeyNavigation = Template.bind({});
// KeyNavigation.args = {
//   controlContext: mockControlContext,
// };
// KeyNavigation.play = async ({ canvasElement }) => {
//   const input = canvasElement.querySelector('input') as HTMLInputElement;
//   input.focus();
//   const event = new KeyboardEvent('keydown', { key: 'ArrowDown' });
//   input.dispatchEvent(event);
//   console.log('Key down navigation triggered');
// };