/*
 * Copyright(c) RIB Software GmbH
 */

import { ILayoutConfiguration } from '@libs/ui/common';
import { prefixAllTranslationKeys} from '@libs/platform/common';
import {Injectable} from '@angular/core';
import { ISalesWipEstLineItemEntity } from '@libs/sales/interfaces';
import { SalesWipLabels } from '../sales-wip-labels.class';

@Injectable({
    providedIn: 'root',
})

export class WipEstLineItemBaseLayoutService {
    public generateLayout(): ILayoutConfiguration<ISalesWipEstLineItemEntity> {
        return {
            groups: [
                {
                    gid: 'basicData',
                    title: {
                        key: 'cloud.common.entityProperties',
                        text: 'Basic Data',
                    },
                    attributes: [
                        'ProjectNo',
                        'ProjectName',
                        'EstimationCode',
                        'EstimationDescription',
                        'OrderChangeFk',
                        'EstAssemblyFk',
                        'Code',
                        'DescriptionInfo',
                        'BasUomTargetFk',
                        'BasUomFk',
                        'PrjChangeFk',
                        'Budget',
                        'BudgetUnit',
                        'BudgetDifference',
                        'Revenue',
                        'EstLineItemStatusFk',
                        'RevenueUnit',
                        'Margin1',
                        'Margin2',
                        'AssemblyType',
                        'EstAssemblyDescriptionInfo',
                        'ExternalCode',
                        'Info',
                        'ItemInfo',
                        'PrjChangeStatusFk',
                    ],
                },
                {
                    gid: 'references',
                    title: {
                        key: 'estimate.main.references',
                        text: 'References',
                    },
                    attributes: ['EstLineItemFk', 'EstCostRiskFk'],
                },
                {
                    gid: 'ruleAndParam',
                    title: {
                        key: 'estimate.main.ruleAndParam',
                        text: 'Rule/Parameter',
                    },
                    attributes: ['Rule', 'Param'],
                },
                {
                    gid: 'itemQuantity',
                    title: {
                        key: 'estimate.main.itemQuantity',
                        text: 'Item Quantity',
                    },
                    attributes: ['QuantityTarget', 'QuantityTargetDetail', 'WqQuantityTarget', 'WqQuantityTargetDetail'],
                },
                {
                    gid: 'quantityRelation',
                    title: {
                        key: 'estimate.main.quantityRelation',
                        text: 'Quantity Relation',
                    },
                    attributes: ['EstQtyRelBoqFk', 'EstQtyRelActFk', 'EstQtyRelGtuFk', 'EstQtyTelAotFk'],
                },
                {
                    gid: 'quantiyAndFactors',
                    title: {
                        key: 'estimate.main.quantityAndFactors',
                        text: 'Quantiy/Factors',
                    },
                    attributes: [
                        'QuantityDetail',
                        'Quantity',
                        'QuantityFactorDetail1',
                        'QuantityFactor1',
                        'QuantityFactorDetail2',
                        'QuantityFactor2',
                        'QuantityFactor3',
                        'QuantityFactor4',
                        'ProductivityFactorDetail',
                        'ProductivityFactor',
                        'QuantityUnitTarget',
                        'QuantityTotal',
                        'Co2SourceTotal',
                        'Co2ProjectTotal',
                        'Co2TotalVariance',
                    ],
                },
                {
                    gid: 'costFactors',
                    title: {
                        key: 'estimate.main.costFactors',
                        text: 'Cost Factors',
                    },
                    attributes: ['CostFactorDetail1', 'CostFactor1', 'CostFactorDetail2', 'CostFactor2'],
                },
                {
                    gid: 'costAndHours',
                    title: {
                        key: 'estimate.main.costAndHours',
                        text: 'Cost/Hours',
                    },
                    attributes: ['CostUnit', 'CostUnitTarget', 'CostTotal', 'HoursUnit', 'HoursUnitTarget', 'HoursTotal', 'BaseCostUnit', 'BaseCostTotal'],
                },
                {
                    gid: 'directIndCost',
                    title: {
                        key: 'estimate.main.directIndCost',
                        text: 'Direct/Indirect Cost',
                    },
                    attributes: [
                        'EntCostUnit',
                        'DruCostUnit',
                        'DirCostUnit',
                        'IndCostUnit',
                        'EntCostUnitTarget',
                        'DruCostUnitTarget',
                        'DirCostUnitTarget',
                        'IndCostUnitTarget',
                        'EntCostTotal',
                        'DruCostTotal',
                        'DirCostTotal',
                        'IndCostTotal',
                        'EntHoursUnit',
                        'DruHoursUnit',
                        'DirHoursUnit',
                        'IndHoursUnit',
                        'EntHoursUnitTarget',
                        'DruHoursUnitTarget',
                        'DirHoursUnitTarget',
                        'IndHoursUnitTarget',
                        'EntHoursTotal',
                        'DruHoursTotal',
                        'DirHoursTotal',
                        'IndHoursTotal',
                        'MarkupCostUnit',
                        'MarkupCostUnitTarget',
                        'MarkupCostTotal',
                        'GrandTotal',
                        'RiskCostUnit',
                        'RiskCostTotal',
                        'EscalationCostUnit',
                        'EscalationCostTotal',
                        'GrandCostUnit',
                        'GrandCostUnitTarget',
                        'DayWorkRateTotal',
                        'DayWorkRateUnit',
                    ],
                },
                {
                    gid: 'flags',
                    title: {
                        key: 'estimate.main.flags',
                        text: 'Flags',
                    },
                    attributes: ['IsLumpsum', 'IsDisabled', 'IsGc', 'IsNoMarkup', 'IsFixedBudget', 'IsOptional', 'IsNoEscalation', 'IsIncluded', 'NoLeadQuantity', 'IsFixedPrice', 'IsOptionalIT', 'IsFixedBudgetUnit'],
                },
                {
                    gid: 'assignments',
                    title: {
                        key: 'estimate.main.assignments',
                        text: 'Assignments',
                    },
                    attributes: [
                        'MdcControllingUnitFk',
                        'BoqItemFk',
                        'PsdActivityFk',
                        'MdcWorkCategoryFk',
                        'MdcAssetMasterFk',
                        'PrjLocationFk',
                        'LgmJobFk',
                        'WicBoqItemFk',
                        'WicBoqItemFk',
                        'BoqWicCatFk',
                        'BoqSplitQuantityFk',
                        'BoqHeaderFk',
                        'PsdActivityFk',
                    ],
                },
                {
                    gid: 'sortCodes',
                    title: {
                        key: 'estimate.main.sortCodes',
                        text: 'Sort Codes',
                    },
                    attributes: [
                        'SortDesc01Fk',
                        'SortDesc02Fk',
                        'SortDesc03Fk',
                        'SortDesc04Fk',
                        'SortDesc05Fk',
                        'SortDesc06Fk',
                        'SortDesc07Fk',
                        'SortDesc08Fk',
                        'SortDesc09Fk',
                        'SortDesc10Fk',
                        'SortCode01Fk',
                        'SortCode02Fk',
                        'SortCode03Fk',
                        'SortCode04Fk',
                        'SortCode05Fk',
                        'SortCode06Fk',
                        'SortCode07Fk',
                        'SortCode08Fk',
                        'SortCode09Fk',
                        'SortCode10Fk',
                    ],
                },
                {
                    gid: 'packageAndCos',
                    title: {
                        key: 'estimate.main.packageAndCos',
                        text: 'Package/COS',
                    },
                    attributes: ['PackageAssignments', 'CosInstanceCode', 'CosInstanceDescription', 'CosMasterHeaderCode', 'CosMasterHeaderDescription', 'PrcStructureFk'],
                },
                {
                    gid: 'duration',
                    title: {
                        key: 'estimate.main.duration',
                        text: 'Duration',
                    },
                    attributes: ['FromDate', 'ToDate'],
                },
                {
                    gid: 'userDefText',
                    title: {
                        key: 'cloud.common.UserdefTexts',
                        text: 'User-Defined Texts',
                    },
                    attributes: ['UserDefined1', 'UserDefined2', 'UserDefined3', 'UserDefined4', 'UserDefined5', 'CommentText', 'Hint', 'CosMatchText'],
                },
                {
                    gid: 'currencies',
                    title: {
                        key: 'estimate.main.currencies',
                        text: 'Currencies',
                    },
                    attributes: ['CostExchangeRate1', 'CostExchangeRate2', 'ForeignBudget1', 'ForeignBudget2', 'Currency1Fk', 'Currency2Fk'],
                },
                {
                    gid: 'FormFk',
                    title: {
                        key: 'estimate.main.formFk',
                        text: 'User Form',
                    },
                    attributes: ['FormFk'],
                },
                {
                    gid: 'Allowance',
                    title: {
                        key: 'estimate.main.allowance',
                        text: 'Allowance',
                    },
                    attributes: [
                        'ManualMarkupUnitItem',
                        'ManualMarkupUnit',
                        'ManualMarkup',
                        'AdvancedAllUnitItem',
                        'AdvancedAllUnit',
                        'AdvancedAll',
                        'GcUnitItem',
                        'GcUnit',
                        'Gc',
                        'GaUnitItem',
                        'GaUnit',
                        'Ga',
                        'AmUnitItem',
                        'AmUnit',
                        'Am',
                        'RpUnitItem',
                        'RpUnit',
                        'Rp',
                        'AllowanceUnitItem',
                        'AllowanceUnit',
                        'Allowance',
                        'Fm',
                        'URDUnitItem',
                        'URD',
                    ],
                },
            ],
            labels: {
                ...SalesWipLabels.getSalesWipLabels(),
                ...prefixAllTranslationKeys('estimate.main.', {
                    OrderChangeFk: {
                        key: 'ordHeaderFk',
                        text: 'Change Order',
                    },
                }),
                ...prefixAllTranslationKeys('cloud.common.', {
                    PackageAssignments: {
                        key: 'entityPackage',
                        text: 'Package',
                    },
                    EstLineItemStatusFk: {
                        key: 'entityStatus',
                        text: 'Status',
                    },
                }),
                ...prefixAllTranslationKeys('estimate.main.', {
                    WicBoqItemFk: {
                        key: 'wicBoqItemFk',
                        text: 'WIC BoQ -Item Ref.No',
                    },
                    BoqWicCatFk: {
                        key: 'boqWicCatFk',
                        text: 'WIC Group Ref.No',
                    },
                    BudgetDifference: {
                        key: 'budgetDiff',
                        text: 'Budget Difference',
                    },
                    Revenue: {
                        key: 'revenue',
                        text: 'Revenue',
                    },
                    CostExchangeRate1: {
                        key: 'costExchangeRate1',
                        text: 'Cost Foreign Total 1',
                    },
                    CostExchangeRate2: {
                        key: 'costExchangeRate2',
                        text: 'Cost Foreign Total 2',
                    },
                    WqQuantityTarget: {
                        key: 'wqQuantityTarget',
                        text: ' Wq Quantity Item',
                    },
                    RevenueUnit: {
                        key: 'revenueUnit',
                        text: 'Revenue/Unit',
                    },
                    Margin1: {
                        key: 'margin1',
                        text: 'Margin1(Revenue-CostTotal)',
                    },
                    Margin2: {
                        key: 'margin2',
                        text: 'Margin2(Revenue-GrandTotal)',
                    },
                    MarkupCostUnit: {
                        key: 'markupCostUnit',
                        text: 'Markup Cost/Unit',
                    },
                    MarkupCostUnitTarget: {
                        key: 'markupCostUnitTarget',
                        text: 'Markup Cost/Unit Item',
                    },
                    MarkupCostTotal: {
                        key: 'markupCostTotal',
                        text: 'Markup Cost Total',
                    },
                    GrandTotal: {
                        key: 'grandTotal',
                        text: 'Grand Total',
                    },
                    WqQuantityTargetDetail: {
                        key: 'wqQuantityTargetDetail',
                        text: 'Wq Quantity Target Detail',
                    },
                    RiskCostUnit: {
                        key: 'costRiskUnit',
                        text: 'Risk Cost/Unit',
                    },
                    RiskCostTotal: {
                        key: 'costRiskTotal',
                        text: 'Risk Cost Total',
                    },
                    BoqSplitQuantityFk: {
                        key: 'boqSplitQuantity',
                        text: 'Boq Split Quantity',
                    },
                    EscalationCostUnit: {
                        key: 'escalationCostUnit',
                        text: 'Escalation Cost/Unit',
                    },
                    IsNoEscalation: {
                        key: 'isNoEscalation',
                        text: 'No Escalation',
                    },
                    EscalationCostTotal: {
                        key: 'escalationCostTotal',
                        text: 'Escalation Cost Total',
                    },
                    ForeignBudget1: {
                        key: 'foreignBudget1',
                        text: 'Foreign Budget 1',
                    },
                    ForeignBudget2: {
                        key: 'foreignBudget2',
                        text: 'Foreign Budget 2',
                    },
                    IsIncluded: {
                        key: 'isIncluded',
                        text: 'Included',
                    },
                    BaseCostUnit: {
                        key: 'baseCostUnit',
                        text: 'Base Cost/Unit',
                    },
                    BaseCostTotal: {
                        key: 'baseCostTotal',
                        text: 'Base Cost Total',
                    },
                    GrandCostUnit: {
                        key: 'grandCostUnit',
                        text: 'Grand Cost/Unit',
                    },
                    GrandCostUnitTarget: {
                        key: 'grandCostUnitTarget',
                        text: 'Grand Cost/Unit Item',
                    },
                    DayWorkRateTotal: {
                        key: 'dayWorkRateTotal',
                        text: 'DW/T+M Rate Total',
                    },
                    DayWorkRateUnit: {
                        key: 'dayWorkRateUnit',
                        text: 'DW/T+M Rate/Unit',
                    },
                    AssemblyType: {
                        key: 'assemblyType',
                        text: 'Assembly Type',
                    },
                    ManualMarkupUnitItem: {
                        key: 'manualMarkupUnitItem',
                        text: 'Manual Markup/Unit Item',
                    },
                    ManualMarkupUnit: {
                        key: 'manualMarkupUnit',
                        text: 'Manual Markup/Unit',
                    },
                    ManualMarkup: {
                        key: 'manualMarkup',
                        text: 'Manual Markup',
                    },
                    AdvancedAllUnitItem: {
                        key: 'advancedAllUnitItem',
                        text: 'Adv. Allowance/Unit Item',
                    },
                    AdvancedAllUnit: {
                        key: 'advancedAllUnit',
                        text: 'Adv. Allowance/Unit',
                    },
                    AdvancedAll: {
                        key: 'advancedAll',
                        text: 'Adv. Allowance',
                    },
                    GcUnitItem: {
                        key: 'gcUnitItem',
                        text: 'GC/Unit Item',
                    },
                    GcUnit: {
                        key: 'gcUnit',
                        text: 'GC/Unit',
                    },
                    Gc: {
                        key: 'gc',
                        text: 'GC',
                    },
                    GaUnitItem: {
                        key: 'gaUnitItem',
                        text: 'G&A/Unit Item',
                    },
                    GaUnit: {
                        key: 'gaUnit',
                        text: 'G&A/Unit',
                    },
                    Ga: {
                        key: 'ga',
                        text: 'G&A',
                    },
                    AmUnitItem: {
                        key: 'amUnitItem',
                        text: 'AM/Unit Item',
                    },
                    AmUnit: {
                        key: 'amUnit',
                        text: 'AM/Unit',
                    },
                    Am: {
                        key: 'am',
                        text: 'AM',
                    },
                    RpUnitItem: {
                        key: 'rpUnitItem',
                        text: 'R&P/Unit Item',
                    },
                    RpUnit: {
                        key: 'rpUnit',
                        text: 'R&P/Unit',
                    },
                    Rp: {
                        key: 'rp',
                        text: 'R&P',
                    },
                    AllowanceUnitItem: {
                        key: 'allowanceUnitItem',
                        text: 'Allowance/Unit Item',
                    },
                    AllowanceUnit: {
                        key: 'allowanceUnit',
                        text: 'Allowance/Unit',
                    },
                    Allowance: {
                        key: 'allowance',
                        text: 'Allowance',
                    },
                    Fm: {
                        key: 'fm',
                        text: 'FM',
                    },
                    URDUnitItem: {
                        key: 'urdunititem',
                        text: 'URD/Unit item',
                    },
                    URD: {
                        key: 'urd',
                        text: 'URD',
                    },
                    IsOptionalIT: {
                        key: 'isoptionalit',
                        text: 'Is Optional IT',
                    },
                    IsFixedBudgetUnit: {
                        key: 'isFixedBudgetUnit',
                        text: 'Fix Budget/Unit',
                    },
                    EstAssemblyDescriptionInfo: {
                        key: 'estAssemblyDescriptionInfo',
                        text: 'Assembly Description',
                    },
                }),
                ...prefixAllTranslationKeys('project.structures.', {
                    SortDesc01Fk: {
                        key: 'sortDesc01',
                        text: 'Sort Description 1',
                    },
                    SortDesc02Fk: {
                        key: 'sortDesc02',
                        text: 'Sort Description 2',
                    },
                    SortDesc03Fk: {
                        key: 'sortDesc03',
                        text: 'Sort Description 3',
                    },
                    SortDesc04Fk: {
                        key: 'sortDesc04',
                        text: 'Sort Description 4',
                    },
                    SortDesc05Fk: {
                        key: 'sortDesc05',
                        text: 'Sort Description 5',
                    },
                    SortDesc06Fk: {
                        key: 'sortDesc06',
                        text: 'Sort Description 6',
                    },
                    SortDesc07Fk: {
                        key: 'sortDesc07',
                        text: 'Sort Description 7',
                    },
                    SortDesc08Fk: {
                        key: 'sortDesc08',
                        text: 'Sort Description 8',
                    },
                    SortDesc09Fk: {
                        key: 'sortDesc09',
                        text: 'Sort Description 9',
                    },
                    SortDesc10Fk: {
                        key: 'sortDesc10',
                        text: 'Sort Description 10',
                    },
                    SortCode01Fk: {
                        key: 'sortCode01',
                        text: 'Sort Code 1',
                    },
                    SortCode02Fk: {
                        key: 'sortCode02',
                        text: 'Sort Code 2',
                    },
                    SortCode03Fk: {
                        key: 'sortCode03',
                        text: 'Sort Code 3',
                    },
                    SortCode04Fk: {
                        key: 'sortCode04',
                        text: 'Sort Code 4',
                    },
                    SortCode05Fk: {
                        key: 'sortCode05',
                        text: 'Sort Code 5',
                    },
                    SortCode06Fk: {
                        key: 'sortCode06',
                        text: 'Sort Code 6',
                    },
                    SortCode07Fk: {
                        key: 'sortCode07',
                        text: 'Sort Code 7',
                    },
                    SortCode08Fk: {
                        key: 'sortCode08',
                        text: 'Sort Code 8',
                    },
                    SortCode09Fk: {
                        key: 'sortCode09',
                        text: 'Sort Code 9',
                    },
                    SortCode10Fk: {
                        key: 'sortCode10',
                        text: 'Sort Code 10',
                    },
                }),
                ...prefixAllTranslationKeys('boq.main.', {
                    IsFixedPrice: {
                        key: 'IsFixedPrice',
                        text: 'Fixed Price',
                    },
                    ExternalCode: {
                        key: 'ExternalCode',
                        text: 'External Code',
                    },
                }),
                ...prefixAllTranslationKeys('basics.common.', {
                    Co2SourceTotal: {
                        key: 'sustainabilty.entityCo2SourceTotal',
                        text: 'CO2 (Source) Total',
                    },
                    Co2ProjectTotal: {
                        key: 'sustainabilty.entityCo2ProjectTotal',
                        text: 'CO2 (Project) Total',
                    },
                    Co2TotalVariance: {
                        key: 'sustainabilty.entityCo2TotalVariance',
                        text: 'CO2 Total Variance',
                    },
                }),
            },
            overloads:{},
            suppressHistoryGroup: true
        };
    }
}
