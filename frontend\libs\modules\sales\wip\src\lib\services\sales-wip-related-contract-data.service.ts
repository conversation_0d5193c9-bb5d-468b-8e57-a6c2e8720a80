/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { DataServiceFlatLeaf, EntityDateProcessorFactory, IDataServiceChildRoleOptions, IDataServiceEndPointOptions, IDataServiceOptions, IEntityProcessor, ServiceRole } from '@libs/platform/data-access';
import { WipHeaderComplete } from '../model/complete-class/wip-header-complete.class';
import { SalesWipWipsDataService } from './sales-wip-wips-data.service';
import { IOrdHeaderEntity, IWipHeaderEntity } from '@libs/sales/interfaces';
import { SalesWipReadonlyProcessorService } from './sales-wip-readonly-processor.service';

@Injectable({
	providedIn: 'root'
})

export class SalesWipRelatedContractDataService extends DataServiceFlatLeaf<IOrdHeaderEntity, IWipHeaderEntity, WipHeaderComplete> {

	public constructor(dataService: SalesWipWipsDataService) {
		const options: IDataServiceOptions<IOrdHeaderEntity> = {
			apiUrl: 'sales/contract',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'contractsbyWipId',
				usePost: true
			},
			roleInfo: <IDataServiceChildRoleOptions<IOrdHeaderEntity, IWipHeaderEntity, WipHeaderComplete>>{
				role: ServiceRole.Leaf,
				itemName: 'OrdHeader',
				parent: dataService
			},
			entityActions: {
                deleteSupported: false,
                createSupported: false
            }
		};
		super(options);

		this.processor.addProcessor([
			//TODO: SalesContractDocumentTypeProcessor
			this.provideDateProcessor(), 
			new SalesWipReadonlyProcessorService<IOrdHeaderEntity>(this)
		]);
	}

	private provideDateProcessor(): IEntityProcessor<IOrdHeaderEntity> {
		const dateProcessorFactory = inject(EntityDateProcessorFactory);
		return dateProcessorFactory.createProcessorFromSchemaInfo<IOrdHeaderEntity>({ moduleSubModule: 'Sales.Contract', typeName: 'OrdHeaderDto' });
	}
}