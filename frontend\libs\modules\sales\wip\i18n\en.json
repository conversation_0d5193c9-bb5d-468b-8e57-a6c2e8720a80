{"sales": {"wip": {"containerTitleWips": "WIP", "containerTitleWipsForm": "WIP Details", "containerTitleWipsTranslation": "Translation", "containerWipBoqsTitle": "BoQs", "entityDocumentDate": "Document Date", "entityWipStatusFk": "WIP (Status)", "entityWipValue": "WIP (Value)", "entityWipValueOc": "WIP (Value OC)", "entityWipVat": "WIP (VAT)", "entityWipVatOc": "WIP (VAT OC)", "errorNoStatusForSelectedCategory": "There is no WIP status for the selected rubric category", "goToSalesWip": "Open WIP", "wizardCSChangeWipStatus": "Change WIP Status", "noCurrentWipSelection": "Please select a WIP", "wizardCWCreateBill": "Create <PERSON> from WIP", "wizardCWCreateBillConfirmDlg": "Bill will be created", "entityIsBilled": "Is Billed", "previousWip": "Previous WIP", "noWipHeaderSelected": "There is no WIP selected! Please select one!", "performedFrom": "Performed From", "performedTo": "Performed To", "wipSelectionMissing": "No WIP selected", "wizardCWCreateBillWipNotOrderedInfo": "The selected WIP is not approved yet. Check the WIP status first.", "createWIPFail": "Create W<PERSON> Fail", "dateError": "The date PerformedFrom should earlier than date PerformedTo", "assignContractStatusInfo": "Only main contracts with status '{{statuslist}}' are displayed.", "dialog": {"allWIPAssignedMessage": "Not able to delete due to there are write protected QTO references.", "allWIPAssignedMessage2": "WIP refers to a QTO, continue?", "quantitiesAssignedMessage": "WIP has quantities recorded, continue?", "mixedRecordsAssigned": "WIP refers to a QTO and has quantities recorded, continue?"}, "wips": "WIPs", "wizard": {"noCurrentSelection": "Please select a WIP Header first.", "pes": "From PES", "schedule": "From Schedule", "updatedQuantity": "IQ Quantity Updated!", "updateLineItem": "Update LineItem Quantity in Estimate", "updateWipIQ": "Update IQ Quantity"}, "WarningOfDeleteWip": "Delete the Wip!", "containerBillingschema": "Billing <PERSON><PERSON>", "wizardCreateAccrualsTitle": "Create Accruals", "wipIsReadOnly": "The selected WIP is readonly!", "createWarning": "Please check the required values \"Businesspartner\" and \"Subsidiary\" from contract.", "clerk": {"entityClerk": "WIP Clerk", "entityClerkForm": "WIP Clerk Details"}, "dirtyRecalculate": "Recalculate", "entityIsExport": "Export", "entityQtoHeaderFk": "QTO", "createWipTitle": "Create WIP", "errorCodeMustBeUnique": "Code should be unique in a project", "wizardCWCreateBillInProgress": "Bill creation in progress...", "entityWipCode": "WIP Code", "noNewTransactionGenerated": "There is no new new transaction generated, please check amount or status!", "entityIsNotAccrual": "Is not accrual", "estimateLineItemGridContainer": "Line Items", "LiPercentageQuantity": "Percentage Quantity", "LiPreviousQuantity": "Previous Quantity", "LiQuantity": "IQ Quantity", "LiTotalQuantity": "IQ Total Quantity", "AssignmentError": "No available QTO lines to create/update {{param}}.", "warningOfQtoLineWithValidationError": "There are QTO lines with validation errors. Please correct the errors before transferring the quantities.", "containerTitleAccrual": "Accruals", "accrualMode": "Accrual Mode", "entityBillingStatus": "Billing Status", "entityCompanyTransactionFk": "Company Transaction", "entityDateEffective": "Date Effective", "transaction": {"account": "Account", "accountingJournals": "Accounting Journals", "amount": "Amount", "amountOc": "Amount Oc", "controllingUnitAssign01": "Controlling Unit Assign 01", "controllingUnitAssign01Desc": "Controlling Unit Assign 01 Description", "controllingUnitAssign02": "Controlling Unit Assign 02", "controllingUnitAssign02Desc": "Controlling Unit Assign 02 Description", "controllingUnitAssign03": "Controlling Unit Assign 03", "controllingUnitAssign03Desc": "Controlling Unit Assign 03 Description", "controllingUnitAssign04": "Controlling Unit Assign 04", "controllingUnitAssign04Desc": "Controlling Unit Assign 04 Description", "controllingUnitAssign05": "Controlling Unit Assign 05", "controllingUnitAssign06": "Controlling Unit Assign 06", "controllingUnitAssign06Desc": "Controlling Unit Assign 06 Description", "controllingUnitAssign07": "Controlling Unit Assign 07", "controllingUnitAssign07Desc": "Controlling Unit Assign 07 Description", "controllingUnitAssign08": "Controlling Unit Assign 08", "controllingUnitAssign09": "Controlling Unit Assign 09", "controllingUnitAssign09Desc": "Controlling Unit Assign 09 Description", "controllingUnitAssign10": "Controlling Unit Assign 10", "controllingUnitCode": "Controlling Unit Code", "currency": "<PERSON><PERSON><PERSON><PERSON>", "documentType": "Document Type", "group": "Transaction", "nominalDimension": "Nominal Dimension", "offsetAccount": "Offset Account", "offsetContUnitAssign01Desc": "Offset Controlling Unit Assign 01 Description", "offsetContUnitAssign02": "Offset Controlling Unit Assign 02", "offsetContUnitAssign02Desc": "Offset Controlling Unit Assign 02 Description", "offsetContUnitAssign03": "Offset Controlling Unit Assign 03", "offsetContUnitAssign03Desc": "Offset Controlling Unit Assign 03 Description", "offsetContUnitAssign05": "Offset Controlling Unit Assign 05", "offsetContUnitAssign05Desc": "Offset Controlling Unit Assign 05 Description", "offsetContUnitAssign06Desc": "Offset Controlling Unit Assign 06 Description", "offsetContUnitAssign07": "Offset Controlling Unit Assign 07", "offsetContUnitAssign07Desc": "Offset Controlling Unit Assign 07 Description", "offsetContUnitAssign09Desc": "Offset Controlling Unit Assign 09 Description", "postingArea": "Posting Area", "postingDate": "Posting Date", "postingNarritive": "Posting Narritive", "quantity": "Quantity", "taxCode": "Tax Code", "transHeader": "Transaction Header", "voucherDate": "Voucher Date", "voucherNumber": "Voucher Number", "controllingUnitAssign05Desc": "Controlling Unit Assign 05 Description", "controllingUnitAssign08Desc": "Controlling Unit Assign 08 Description", "controllingUnitAssign10Desc": "Controlling Unit Assign 10 Description", "offsetContUnitAssign01": "Offset Controlling Unit Assign 01", "offsetContUnitAssign04": "Offset Controlling Unit Assign 04", "offsetContUnitAssign04Desc": "Offset Controlling Unit Assign 04 Description", "offsetContUnitAssign06": "Offset Controlling Unit Assign 06", "offsetContUnitAssign08": "Offset Controlling Unit Assign 08", "offsetContUnitAssign08Desc": "Offset Controlling Unit Assign 08 Description", "offsetContUnitAssign09": "Offset Controlling Unit Assign 09", "offsetContUnitAssign10": "Offset Controlling Unit Assign 10", "offsetContUnitAssign10Desc": "Offset Controlling Unit Assign 10 Description", "offsetContUnitCode": "Offset Controlling Unit Code"}, "transactionType": "Transaction Type", "voucherNo": "Voucher No", "LiCumulativePercentage": "Cumulative Percentage", "relatedBills": "Related Bills", "containerTitleContracts": "Related Contracts", "wizardCWCreateBillWipNotAcceptedInfo": "The selected wip is not accepted yet. Check the wip status first.", "wizardCreateAccrualsNoTransactionsGeneratedInfo": "No transactions were generated.", "wizardCreateAccrualsNoPostingPeriod": "No transactions were generated. There is no company period for the specified posting date.", "wizardCreateAccrualsPostingPeriodNotOpen": "No transactions were generated. Posting Date falls into accounting period: {{period}} which is not open.", "wizardCreateAccrualsNotInPeriod": "No transactions were generated. Posting Date does not fall into accounting period: {{period}}.", "entityFactorDJC": "Factor on DJC", "entityMessage": "Message", "entityMessageseverityFk": "Message Severity", "entityChangeSalesWipConfig": "Change Wip Type/Configuration"}}}