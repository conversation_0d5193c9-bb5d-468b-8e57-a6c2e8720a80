import { <PERSON>a, StoryObj, moduleMetadata } from '@storybook/angular';
import { ActionComponent } from './action.component';
import { CommonModule } from '@angular/common';
import { ControlContextInjectionToken } from '../../model/control-context.interface';
import { ISimpleMenuItem } from '../../../model/menu-list/interface/simple-menu-item.interface';
import { FieldDataMode } from '../../../model/fields/field-data-mode.enum';
import { Component, Input } from '@angular/core';
import { ItemType } from '../../../model/menu-list/enum/menulist-item-type.enum';
import { PlatformConfigurationService, PlatformTranslateService, TranslatePipe } from '@libs/platform/common';
import { HttpClientModule } from '@angular/common/http';
import { IMenuItemsList } from '../../../model/menu-list/interface';
@Component({
  selector: 'ui-common-menu-list',
  template: `<ul><li *ngFor="let item of menu.items">
    <button (click)="item.fn()">{{item.caption?.key}}</button>
  </li></ul>`
})
class MenuListStubComponent {
  @Input() public menu: IMenuItemsList | undefined;
}

interface ITestEntity {
  myText?: string;
}
// Mock entity and context
const mockEntity:ITestEntity = { myText: 'Hello from action component!' };

// Use ISimpleMenuItem to ensure type compatibility
const mockAction: ISimpleMenuItem<ISimpleMenuItem<void>> = {
  id: 'actionButton',
  type: ItemType.Item,
  iconClass: 'control-icons ico-config-test',
  fn: () => { 
    alert('Action button 1 click'); 
  },
  caption: { key: 'basics.customize.checkConfig' },
};

const mockControlContext = {
  fieldId: 'actionField',
  value: [mockAction] as ISimpleMenuItem<ISimpleMenuItem<void>>[],
  maxLength: 0,
  placeholder: '',
  readonly: false,
  validationResults: [],
  entityContext: { entity: mockEntity, totalCount: 0 },
  actionsSource: FieldDataMode.ModelElseFieldDef,
  actions: [mockAction],
   displayText: {
    getValue: (item: ISimpleMenuItem<void>) => {
      const caption = item.caption;
      if (typeof caption === 'object' && caption !== null && 'key' in caption) {
        return caption.key;
      }
      if (typeof caption === 'string') {
        return caption;
      }
      if (typeof caption === 'function') {
        return '[Function]';
      }
      return 'Hello from action component!';
    }
  },
  options: {},
};

const meta: Meta<ActionComponent<ISimpleMenuItem<void>>> = {
  title: 'UI Common/Domain Controls/ActionComponent',
  component: ActionComponent,
  decorators: [
    moduleMetadata({
      imports: [CommonModule,HttpClientModule,TranslatePipe],
      declarations: [MenuListStubComponent],
      providers: [
        PlatformTranslateService,
        PlatformConfigurationService,
        { provide: ControlContextInjectionToken, useValue: mockControlContext },
      ],
    }),
  ],
  render: (args) => ({
    props: {
      controlContext: args.controlContext,
    },
  }),
};

export default meta;
type Story = StoryObj<ActionComponent<ISimpleMenuItem<void>>>;

export const Default: Story = {
  args: {
    controlContext: mockControlContext,
  },
};