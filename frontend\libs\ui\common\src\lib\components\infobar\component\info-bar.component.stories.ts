import { <PERSON>a, StoryObj, moduleMetadata } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { UiCommonInfoBarComponent } from './info-bar.component';
import { Pipe, PipeTransform } from '@angular/core';

import { HttpClientModule } from '@angular/common/http';
import { PlatformConfigurationService, PlatformTranslateService } from '@libs/platform/common';

// Stub for platformTranslate pipe
@Pipe({ name: 'platformTranslate' })
class PlatformTranslatePipeStub implements PipeTransform {
  public transform(value: string | { text: string } | undefined): string  {
      if (typeof value === 'string') {
      return value;
    }
    return '';
  }
}

// Example data for the info bar
const mockEntityInfos = [
  {
    id: 1,
    description: 'Contract',
    subEntity: [
      {
        id: 11,
        description: 'Contract Detail',
        subEntity: [
          { id: 111, description: 'Project 1' },
          { id: 112, description: 'Project 2' }
        ]
      },
      {
        id: 12,
        description: 'Business Partner',
        subEntity: [
          { id: 121, description: 'RIB Business' }
        ]
      }
    ]
  },
  {
    id: 2,
    description: 'Contract Approvers',
    subEntity: [
      {
        id: 21,
        description: 'Approvers',
        subEntity: [
          { id: 211, description: 'Project 3' },
          { id: 212, description: 'Project 4' }
        ]
      }
    ]
  }
];

export default {
  title: 'UI Common/Components/InfoBar/InfoBar Component',
  component: UiCommonInfoBarComponent,
  decorators: [
    moduleMetadata({
      imports: [CommonModule,HttpClientModule, UiCommonInfoBarComponent,],
      declarations: [
       
        PlatformTranslatePipeStub
      ],
      providers: [
         PlatformTranslateService,
         PlatformConfigurationService
      ]
    }),
  ],
  render: (args) => ({
    props: {
      ...args,
    },
  }),
} as Meta<UiCommonInfoBarComponent>;

type Story = StoryObj<UiCommonInfoBarComponent>;

export const Default: Story = {
  args: {
    entityInfos: mockEntityInfos
  },
};