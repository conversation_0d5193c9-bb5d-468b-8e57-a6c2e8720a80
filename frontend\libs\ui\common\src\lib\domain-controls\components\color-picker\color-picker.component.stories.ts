import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { FormsModule } from '@angular/forms';
import { ColorPickerComponent } from './color-picker.component';
import { IColorControlContext } from '../../model/color-control-context.interfacets';
import { ColorFormat, ColorType, PlatformConfigurationService, RgbColor, MinimalEntityContext, IEntityContext } from '@libs/platform/common';
import { HttpClientModule } from '@angular/common/http';
import { ControlContextInjectionToken } from '../../model/control-context.interface';
import { DebounceChangeDirective } from '../../../directives/ng-model-debounce.directive';

// Create a mock control context
const createMockControlContext = (args: {
	value?: ColorType;
	format?: ColorFormat;
	showClearButton?: boolean;
	infoText?: string;
	readonly?: boolean;
	fieldId?: string;
}): IColorControlContext => ({
	fieldId: args.fieldId || 'colorPicker',
	readonly: args.readonly || false,
	validationResults: [],
	value: args.value,
	format: args.format || ColorFormat.RgbColor,
	showClearButton: args.showClearButton !== undefined ? args.showClearButton : true,
	infoText: args.infoText,
	get entityContext(): IEntityContext<object> {
		return new MinimalEntityContext<object>();
	}
});

// Helper function to create color values based on format
const createColorValue = (colorFormat: ColorFormat, hexColor: string): ColorType | undefined => {
	if (!hexColor){
		return undefined;
	} 
	
	const colorInt = parseInt(hexColor.replace('#', ''), 16);
	
	switch (colorFormat) {
		case ColorFormat.RgbColor:
			return RgbColor.fromRgba(colorInt << 8 | 0xFF); // Add alpha
		case ColorFormat.RgbaValue:
			return colorInt << 8 | 0xFF; // RGBA format with alpha
		case ColorFormat.ArgbValue:
			return (0xFF << 24) | colorInt; // ARGB format with alpha
		default:
			return RgbColor.fromRgba(colorInt << 8 | 0xFF);
	}
};

export default {
	title: 'UI Common/Domain Controls/ColorPickerComponent',
	component: ColorPickerComponent,
	parameters: {
		docs: {
			description: {
				component: `
A color picker component that provides:
- Visual color selection with color input and text input
- Multiple color format support (RGB, RGBA, ARGB)
- Optional clear button functionality
- Info text display capability
- Readonly mode support
- Built-in validation support

**Key Features:**
- Support for different color formats (ColorFormat.RgbColor, ColorFormat.RgbaValue, ColorFormat.ArgbValue)
- Visual color picker with hex text input
- Optional clear button to reset color selection
- Info text for additional context
- Readonly mode for display-only scenarios
- Integrates with Angular reactive forms
- Validation result display support
			`,
			},
		},
	},
	decorators: [
		moduleMetadata({
			imports: [FormsModule, HttpClientModule, DebounceChangeDirective],
			providers: [
				PlatformConfigurationService
			],
		}),
	],
	argTypes: {
		colorFormat: {
			control: { type: 'select' },
			options: [ColorFormat.RgbColor, ColorFormat.RgbaValue, ColorFormat.ArgbValue],
			description: 'Color format for storing the color value',
		},
		hexColor: {
			control: { type: 'color' },
			description: 'Color value in hex format for display',
		},
		showClearButton: {
			control: { type: 'boolean' },
			description: 'Whether to show the clear button',
		},
		infoText: {
			control: { type: 'text' },
			description: 'Info text displayed next to the color picker',
		},
		readonly: {
			control: { type: 'boolean' },
			description: 'Whether the color picker is readonly',
		},
		fieldId: {
			control: { type: 'text' },
			description: 'Unique field identifier',
		},
	},
	render: (args: { 
		colorFormat?: ColorFormat;
		hexColor?: string;
		showClearButton?: boolean;
		infoText?: string;
		readonly?: boolean;
		fieldId?: string;
	}) => {
		const colorValue = createColorValue(args.colorFormat || ColorFormat.RgbColor, args.hexColor || '#408896');
		
		const controlContext = createMockControlContext({
			value: colorValue,
			format: args.colorFormat,
			showClearButton: args.showClearButton,
			infoText: args.infoText,
			readonly: args.readonly,
			fieldId: args.fieldId,
		});

		return {
			props: {},
			moduleMetadata: {
				providers: [
					{ provide: ControlContextInjectionToken, useValue: controlContext }
				]
			}
		};
	},
} as Meta<ColorPickerComponent & {
	colorFormat?: ColorFormat;
	hexColor?: string;
	showClearButton?: boolean;
	infoText?: string;
	readonly?: boolean;
	fieldId?: string;
}>;

type Story = StoryObj<ColorPickerComponent & {
	colorFormat?: ColorFormat;
	hexColor?: string;
	showClearButton?: boolean;
	infoText?: string;
	readonly?: boolean;
	fieldId?: string;
}>;

export const Default: Story = {
	args: {
		colorFormat: ColorFormat.RgbColor,
		hexColor: '#408896',
		showClearButton: true,
		infoText: 'Select a color',
		readonly: false,
		fieldId: 'colorPicker',
	},
};
