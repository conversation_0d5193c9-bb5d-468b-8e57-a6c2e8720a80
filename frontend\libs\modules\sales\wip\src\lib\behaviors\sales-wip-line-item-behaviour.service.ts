/*
 * Copyright(c) RIB Software GmbH
 */

import {inject, Injectable} from '@angular/core';
import {EntityContainerCommand, IEntityContainerBehavior, IGridContainerLink} from '@libs/ui/business-base';
import {InsertPosition, ItemType} from '@libs/ui/common';
import {SalesWipLineItemDataService} from '../services/sales-wip-line-item-data.service';
import { ISalesWipEstLineItemEntity } from '@libs/sales/interfaces';

@Injectable({
	providedIn: 'root'
})

export class SalesWipLineItemBehaviourService implements IEntityContainerBehavior<IGridContainerLink<ISalesWipEstLineItemEntity>, ISalesWipEstLineItemEntity> {
	public dataService = inject(SalesWipLineItemDataService);

	public onCreate(containerLink: IGridContainerLink<ISalesWipEstLineItemEntity>) {
		containerLink.uiAddOns.toolbar.addItemsAtId(
			{
				id: 'toggleLineItems',
				caption: { text: 'switchLineItems', key: 'switchLineItems' },
				hideItem: false,
				type: ItemType.Item,
				iconClass: 'tlb-icons ico-indirect-costs',
			},
			EntityContainerCommand.CreateRecord,
			InsertPosition.Instead,
		);
	}
}