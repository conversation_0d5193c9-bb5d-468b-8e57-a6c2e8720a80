import { <PERSON>a, StoryObj, moduleMetadata } from '@storybook/angular';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SingleLineTextComponent } from './single-line-text.component';
import { ControlContextInjectionToken } from '../../model/control-context.interface';
import { IStringControlContext } from '../../model/string-control-context.interface';
import { MinimalEntityContext, IEntityContext } from '@libs/platform/common';
import { DebounceChangeDirective } from '../../../directives/ng-model-debounce.directive';

// Create a mock control context
const createMockControlContext = (args: Partial<IStringControlContext<string>>): IStringControlContext<string> => ({
	fieldId: 'singleLineText',
	readonly: false,
	validationResults: [],
	value: '',
	maxLength: 255,
	placeholder: 'Enter text...',
	get entityContext(): IEntityContext<object> {
		return new MinimalEntityContext<object>();
	},
	...args
});

export default {
	title: 'UI Common/Domain Controls/SingleLineTextComponent',
	component: SingleLineTextComponent,
	parameters: {
		docs: {
			description: {
				component: `
A single-line text input component that provides:
- Text input with configurable maximum length
- Customizable placeholder text
- Readonly mode support
- Built-in validation support
- Debounced input changes
			`,
			},
		},
	},
	decorators: [
		moduleMetadata({
			imports: [CommonModule, FormsModule, DebounceChangeDirective],
			providers: []
		}),
	],
	argTypes: {
		maxLength: {
			control: { type: 'number', min: 1, max: 1000, step: 1 },
			description: 'Maximum number of characters allowed',
		},
		placeholder: {
			control: { type: 'text' },
			description: 'Placeholder text shown when input is empty',
		},
		readonly: {
			control: { type: 'boolean' },
			description: 'Whether the input is readonly',
		},
	},
	render: (args: { 
		maxLength?: number; 
		placeholder?: string; 
		readonly?: boolean;
	}) => {
		const controlContext = createMockControlContext({
			maxLength: args.maxLength,
			placeholder: args.placeholder,
			readonly: args.readonly,
		});

		return {
			props: {},
			moduleMetadata: {
				providers: [
					{ provide: ControlContextInjectionToken, useValue: controlContext }
				]
			}
		};
	},
} as Meta<SingleLineTextComponent & {
	maxLength?: number;
	placeholder?: string;
	readonly?: boolean;
}>;

type Story = StoryObj<SingleLineTextComponent & {
	maxLength?: number;
	placeholder?: string;
	readonly?: boolean;
}>;

export const Default: Story = {
	args: {
		maxLength: 255,
		placeholder: 'Enter text...',
		readonly: false,
	},
};

