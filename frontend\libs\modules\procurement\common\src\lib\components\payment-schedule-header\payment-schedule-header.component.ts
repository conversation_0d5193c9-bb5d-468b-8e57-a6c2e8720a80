/*
 * Copyright(c) RIB Software GmbH
 */

import * as math from 'mathjs';
import { merge, cloneDeep } from 'lodash';
import { Subscription } from 'rxjs';
import { IEntityIdentification } from '@libs/platform/common';
import { FieldType, UiCommonMessageBoxService } from '@libs/ui/common';
import { Component, inject, OnDestroy, OnInit } from '@angular/core';
import { BasicsSharedCalculateOverGrossService } from '@libs/basics/shared';
import { IPrcCommonPaymentScheduleTotalSourceContextEntity } from '../../model/entities/prc-payment-schedule-total-source-entity.interface';
import { IPrcCommonPaymentScheduleHeaderInfoEntity } from '../../model/entities/prc-payment-schedule-header-info-entity.interface';
import { PrcCommonPaymentScheduleHeaderInfoToken } from '../../model/interfaces/prc-common-payment-schedule-header-info.interface';

/**
 * Procurement common payment schedule grid container header component
 */
@Component({
	selector: 'procurement-common-payment-schedule-header',
	templateUrl: './payment-schedule-header.component.html',
	styleUrl: './payment-schedule-header.component.scss'
})
export class ProcurementCommonPaymentScheduleHeaderComponent implements OnInit, OnDestroy {
	private readonly messageBoxService = inject(UiCommonMessageBoxService);
	private readonly headerInfo = inject(PrcCommonPaymentScheduleHeaderInfoToken);
	private readonly dataService = inject(this.headerInfo.dataServiceToken);
	public readonly isOverGross = inject(BasicsSharedCalculateOverGrossService).isOverGross;
	public readonly totalSourceTextTr = this.isOverGross ?
		'procurement.common.paymentSchedule.totalSourceGrossOC' :
		'procurement.common.paymentSchedule.totalSourceNetOC';
	public readonly paymentScheduleTargetTextTr = this.isOverGross ?
		'procurement.common.paymentSchedule.paymentScheduleTargetGrossOC' :
		'procurement.common.paymentSchedule.paymentScheduleTargetNetOC';
	private subscriptions: Subscription[] = [];

	public isReadOnly = true;
	public showTotalSettingSection = false;
	public settingInfo: IPrcCommonPaymentScheduleHeaderInfoEntity = {
		TotalNetOc: 0,
		TotalGrossOc: 0,
		PaymentScheduleNetOc: 0,
		PaymentScheduleGrossOc: 0,
		VarianceNetOc: 0,
		VarianceGrossOc: 0
	};
	public totalSourceContextEntity: IPrcCommonPaymentScheduleTotalSourceContextEntity = {
		ParentId: undefined,
		VatPercent: 0,
		SourceNetOc: 0,
		SourceGrossOc: 0,
		ModuleName: this.dataService.moduleName
	};
	public updatedParent: IEntityIdentification | null = null;

	private get parentService() {
		return this.dataService.parentService;
	}

	/**
	 * FieldType Money
	 */
	public get moneyFieldType() {
		return FieldType.Money;
	}

	/**
	 * OnInit
	 */
	public ngOnInit() {
		const parentSelectionChangedSub = this.parentService.selectionChanged$.subscribe(() => {
			this.onParentSelectionChanged();
		});
		const parentUpdatedSub = this.dataService.rootService.entitiesUpdated$.subscribe((parentsUpdated) => {
			this.onParentUpdated(parentsUpdated);
		});
		const listChangedSub = this.dataService.listChanged$.subscribe(() => {
			this.resetPaymentScheduleTarget();
		});
		this.subscriptions.push(parentSelectionChangedSub);
		this.subscriptions.push(parentUpdatedSub);
		this.subscriptions.push(listChangedSub);
	}

	private async onParentSelectionChanged() {
		const parent = this.parentService.getSelectedEntity();
		const rootEntity = this.dataService.rootService.getSelectedEntity();
		this.clearSettingInfo();
		this.isReadOnly = !parent;
		this.showTotalSettingSection = true;
		if (parent && rootEntity) {
			this.resetTotalSourceContext(rootEntity.Id);
			this.showTotalSettingSection = this.dataService.isParentMainEntity(parent);
		}

	}

	/**
	 * Handle on parent update
	 * @param parentsUpdated
	 * @private
	 */
	private onParentUpdated(parentsUpdated: IEntityIdentification[]) {
		if (parentsUpdated && parentsUpdated[0]) {
			this.updatedParent = cloneDeep(parentsUpdated[0]);
		}
	}

	/**
	 * Reset total source context
	 * @param parentId
	 * @private
	 */
	private resetTotalSourceContext(parentId: number) {
		this.totalSourceContextEntity = {
			ParentId: parentId,
			VatPercent: this.dataService.getVatPercent(),
			SourceNetOc: 0,
			SourceGrossOc: 0,
			SourceId: null,
			ModuleName: this.dataService.moduleName
		};
	}

	private resetPaymentScheduleTarget() {
		this.resetSettingInfo({
			PaymentScheduleNetOc: this.dataService.paymentScheduleTarget.netOc,
			PaymentScheduleGrossOc: this.dataService.paymentScheduleTarget.grossOc
		});
	}

	private clearSettingInfo() {
		this.resetSettingInfo({
			TotalNetOc: 0,
			TotalGrossOc: 0,
			PaymentScheduleNetOc: 0,
			PaymentScheduleGrossOc: 0
		});
	}

	private resetSettingInfo(info: Partial<IPrcCommonPaymentScheduleHeaderInfoEntity>) {
		if (this.settingInfo) {
			merge(this.settingInfo, info);
			this.settingInfo.VarianceNetOc = math.round(math.bignumber(this.settingInfo.TotalNetOc).sub(this.settingInfo.PaymentScheduleNetOc), 2).toNumber();
			this.settingInfo.VarianceGrossOc = math.round(math.bignumber(this.settingInfo.TotalGrossOc).sub(this.settingInfo.PaymentScheduleGrossOc), 2).toNumber();
		}
	}

	/**
	 * handle total source value change
	 * @param totalSourceEntity
	 */
	public onTotalSourceChanged(totalSourceEntity: IPrcCommonPaymentScheduleTotalSourceContextEntity) {
		this.resetSettingInfo({
			TotalNetOc: totalSourceEntity.SourceNetOc,
			TotalGrossOc: totalSourceEntity.SourceGrossOc
		});
		this.dataService.totalSource.SourceId = totalSourceEntity.SourceId;
		this.dataService.totalSource.SourceNetOc = totalSourceEntity.SourceNetOc;
		this.dataService.totalSource.SourceGrossOc = totalSourceEntity.SourceGrossOc;
	}

	/**
	 * Disable update target button
	 */
	public disableUpdateTarget(): boolean {
		return this.isReadOnly || this.settingInfo.TotalNetOc === 0 || this.settingInfo.TotalGrossOc === 0;
	}

	public async updatePaymentScheduleTarget() {
		const isSuccessful = await this.dataService.updatePaymentScheduleTarget(this.settingInfo.TotalNetOc, this.settingInfo.TotalGrossOc);
		const parentId = this.parentService.getSelectedEntity()?.Id;
		if (isSuccessful && parent) {
			await this.dataService.load({id: 0, pKey1: parentId});
		}
		this.messageBoxService.showMsgBox({
			headerText: 'procurement.common.paymentSchedule.setPaymentScheduleTotal',
			bodyText: isSuccessful ?
				'procurement.common.paymentSchedule.setPaymentScheduleTotalSuccessfully' :
				'procurement.common.paymentSchedule.setPaymentScheduleTotalFailed',
			iconClass: isSuccessful ?
				'ico-info' :
				'ico-error'
		});
	}

	/**
	 * OnDestroy
	 */
	public ngOnDestroy() {
		this.subscriptions.forEach((sub) => {
			sub.unsubscribe();
		});
	}
}