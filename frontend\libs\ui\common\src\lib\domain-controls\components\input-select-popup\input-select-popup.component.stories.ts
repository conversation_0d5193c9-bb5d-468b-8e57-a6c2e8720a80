import { Meta, Story } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';
import { InputSelectPopupComponent } from './input-select-popup.component';
import { CommonModule } from '@angular/common';
import { Subject } from 'rxjs';
import { IUiInputSelectItem } from '../../../domain-controls/model/ui-input-select-item.interface';
import { ActivePopup } from '../../../popup/model/active-popup';
import EventEmitter from 'events';
import { FormsModule } from '@angular/forms';

const activePopup = new Subject<ActivePopup>();
export default {
  title: 'UI Common/Domain Controls/Input Select Popup',
  component: InputSelectPopupComponent,
  decorators: [
    moduleMetadata({
      declarations: [InputSelectPopupComponent],
      imports: [CommonModule,FormsModule],
      providers: [
        {
          provide: 'items',
          useValue: [
              { id: 1, displayName: 'Estimate', isSelected: false },
              { id: 2, displayName: 'BoQ', isSelected: false },
              { id: 3, displayName: 'WorkFlow', isSelected: false },
          ] as unknown as IUiInputSelectItem[],
        },
        { provide: 'activePopup', useValue: activePopup},
        { provide: 'onSelectedEvent', useValue: new EventEmitter()},
        { provide: 'keyBoardEventSelectedItem',useValue: new Subject<IUiInputSelectItem>(),}
      ]
    })
  ]
} as Meta<InputSelectPopupComponent>;

const Template: Story<InputSelectPopupComponent> = (args: InputSelectPopupComponent) => ({
  props: args,
});

export const Default = Template.bind({});
Default.args = {};
