import { <PERSON>a, StoryObj } from '@storybook/angular';
import { ModalHeaderComponent } from './modal-header.component';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { moduleMetadata } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { PlatformConfigurationService, PlatformTranslateService, TranslatePipe } from '@libs/platform/common';
import { HttpClientModule } from '@angular/common/http';

// Mock cancel function for demonstration
const mockCancel = () => alert('Cancel clicked');

const baseDialogData = {
  dialog: {
    headerText: 'Dialog Title',
    showCloseButton: true,
    cancel: mockCancel,
  },
};

const meta: Meta<ModalHeaderComponent> = {
  title: 'UI Common/Dialogs/Base/Modal Header',
  component: ModalHeaderComponent,
  decorators: [
    moduleMetadata({
      imports: [CommonModule, MatIconModule,HttpClientModule,TranslatePipe],
      declarations: [ModalHeaderComponent],
      providers: [
        PlatformTranslateService,
        PlatformConfigurationService,
        {
          provide: MAT_DIALOG_DATA,
          useValue: baseDialogData,
        },
      ],
    }),
  ],
  render: (args) => ({
    template: `
      <ui-common-modal-header class="modal-header main-color"></ui-common-modal-header>
    `,
    props: {
      ...args,
    },
  }),
  // parameters: {
  //   layout: 'centered',
  // },
};

export default meta;

type Story = StoryObj<ModalHeaderComponent>;

export const Default: Story = {
  args: {},
};

export const WithoutCloseButton: Story = {
  decorators: [
    moduleMetadata({
      providers: [
        {
          provide: MAT_DIALOG_DATA,
          useValue: {
            dialog: {
              ...baseDialogData.dialog,
              showCloseButton: false,
            },
          },
        },
      ],
    }),
  ],
  args: {},
};
