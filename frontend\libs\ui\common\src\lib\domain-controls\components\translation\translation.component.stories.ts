import { <PERSON>a, StoryObj, moduleMetadata } from '@storybook/angular';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslationComponent } from './translation.component';
import { ControlContextInjectionToken, IControlContext } from '../../model/control-context.interface';
import { IDescriptionInfo, MinimalEntityContext, IEntityContext } from '@libs/platform/common';
import { DebounceChangeDirective } from '../../../directives/ng-model-debounce.directive';

// Create a mock control context
const createMockControlContext = (args: {
	translatedText?: string;
	originalDescription?: string;
	descriptionModified?: boolean;
	modified?: boolean;
	readonly?: boolean;
	fieldId?: string;
}): IControlContext<IDescriptionInfo> => {
	const descriptionInfo: IDescriptionInfo = {
		Description: args.originalDescription || 'Original description',
		DescriptionTr: 1,
		DescriptionModified: args.descriptionModified || false,
		Translated: args.translatedText || '',
		VersionTr: 1,
		Modified: args.modified || false,
		OtherLanguages: null,
	};

	return {
		fieldId: args.fieldId || 'translation-input',
		readonly: args.readonly || false,
		validationResults: [],
		value: descriptionInfo,
		get entityContext(): IEntityContext<object> {
			return new MinimalEntityContext<object>();
		}
	};
};

export default {
	title: 'UI Common/Domain Controls/TranslationComponent',
	component: TranslationComponent,
	parameters: {
		docs: {
			description: {
				component: `
A translation input component that provides:
- Text input for translated content
- Support for description information management
- Tracking of modification states
- Version management for translations
- Readonly mode support
- Built-in validation support
			`,
			},
		},
	},
	decorators: [
		moduleMetadata({
			imports: [CommonModule, FormsModule, DebounceChangeDirective],
			providers: []
		}),
	],
	argTypes: {
		translatedText: {
			control: { type: 'text' },
			description: 'The translated text content',
		},
		originalDescription: {
			control: { type: 'text' },
			description: 'The original description text',
		},
		descriptionModified: {
			control: { type: 'boolean' },
			description: 'Whether the original description has been modified',
		},
		modified: {
			control: { type: 'boolean' },
			description: 'Whether the translation has been modified',
		},
		readonly: {
			control: { type: 'boolean' },
			description: 'Whether the input is readonly',
		},
		fieldId: {
			control: { type: 'text' },
			description: 'Unique field identifier',
		},
	},
	render: (args: { 
		translatedText?: string;
		originalDescription?: string;
		descriptionModified?: boolean;
		modified?: boolean;
		readonly?: boolean;
		fieldId?: string;
	}) => {
		const controlContext = createMockControlContext({
			translatedText: args.translatedText,
			originalDescription: args.originalDescription,
			descriptionModified: args.descriptionModified,
			modified: args.modified,
			readonly: args.readonly,
			fieldId: args.fieldId,
		});

		return {
			props: {},
			moduleMetadata: {
				providers: [
					{ provide: ControlContextInjectionToken, useValue: controlContext }
				]
			}
		};
	},
} as Meta<TranslationComponent & {
	translatedText?: string;
	originalDescription?: string;
	descriptionModified?: boolean;
	modified?: boolean;
	readonly?: boolean;
	fieldId?: string;
}>;

type Story = StoryObj<TranslationComponent & {
	translatedText?: string;
	originalDescription?: string;
	descriptionModified?: boolean;
	modified?: boolean;
	readonly?: boolean;
	fieldId?: string;
}>;

export const Default: Story = {
	args: {
		translatedText: '',
		originalDescription: 'Original description',
		descriptionModified: false,
		modified: false,
		readonly: false,
		fieldId: 'translation-input',
	},
};
