<div class="total-source-component flex-box">
	<div class="total-source-lookup">
		<ui-common-lookup-input
			[readonly]="readonly"
			[(value)]="sourceId"
			[dataService]="totalSourceLookupService"
			[entityContext]="context"
			(valueChange)="validateSourceId($event)">
		</ui-common-lookup-input>
	</div>
	<div [ngSwitch]="isOverGross">
		<ui-common-domain-control-host *ngSwitchCase="false" [(value)]="sourceNetOc" [fieldType]="moneyFieldType" (valueChange)="validateTotalSourceNetOc()"></ui-common-domain-control-host>
		<ui-common-domain-control-host *ngSwitchCase="true" [(value)]="sourceGrossOc" [fieldType]="moneyFieldType" (valueChange)="validateTotalSourceGrossOc()"></ui-common-domain-control-host>
	</div>
</div>