/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectableInfo, ILazyInjectionContext, PlatformModuleManagerService } from '@libs/platform/common';
import { BASICS_CLERK_LOOKUP_LAYOUT_GENERATOR, SIMPLE_UPLOAD_SERVICE_TOKEN, ACCESS_SCOPE_UI_HELPER_TOKEN, BASICS_COST_CODES_LOOKUP_PROVIDER_TOKEN, BASICS_CURRENCY_LOOKUP_PROVIDER_TOKEN, BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN, BASICS_MATERIAL_LAYOUT_SERVICE_FACTORY, BASICS_SCOPE_ENTITY_LAYOUT_GENERATOR, BASICS_SCOPE_VALIDATION_SERVICE_FACTORY, BASICS_SCOPE_DETAIL_ENTITY_LAYOUT_GENERATOR, BASICS_SCOPE_DETAIL_VALIDATION_SERVICE_FACTORY, BASICS_PRC_CONFIG_LOOKUP_LAYOUT_GENERATOR, BASICS_SITE_LOOKUP_PROVIDER_TOKEN, BASICS_CUSTOMIZE_LOOKUP_PROVIDER_TOKEN, BASICS_EFBSHEETS_LOOKUP_PROVIDER_TOKEN, BASICS_CHARACTERISTIC_DISCRETE_VALUE_DATA_PROVIDER } from '@libs/basics/interfaces';


export const LAZY_INJECTABLES: LazyInjectableInfo[] =[
LazyInjectableInfo.create('basics.clerk.BasicClerkLookupColumnGeneratorService', BASICS_CLERK_LOOKUP_LAYOUT_GENERATOR, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/clerk');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.BasicClerkLookupColumnGeneratorService) : null;
		
	}),
LazyInjectableInfo.create('basics.common.SimpleUploadService', SIMPLE_UPLOAD_SERVICE_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/common');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.SimpleUploadService) : null;
		
	}),
LazyInjectableInfo.create('basics.common.BasicsCommonAccessScopeUiHelperService', ACCESS_SCOPE_UI_HELPER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/common');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.BasicsCommonAccessScopeUiHelperService) : null;
		
	}),
LazyInjectableInfo.create('basics.costcodes.BasicsCostCodeLookupProviderService', BASICS_COST_CODES_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/costcodes');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.BasicsCostCodeLookupProviderService) : null;
		
	}),
LazyInjectableInfo.create('basics.currency.BasicsCurrencyLookupProviderService', BASICS_CURRENCY_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/currency');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.BasicsCurrencyLookupProviderService) : null;
		
	}),
LazyInjectableInfo.create('basics.efbsheets.BasicsEfbsheetsCommonService', BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/efbsheets');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.BasicsEfbsheetsCommonService) : null;
		
	}),
LazyInjectableInfo.create('basics.material.BasicsMaterialCommonLayoutService', BASICS_MATERIAL_LAYOUT_SERVICE_FACTORY, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/material');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.BasicsMaterialCommonLayoutService) : null;
		
	}),
LazyInjectableInfo.create('basics.material.BasicsMaterialScopeLayoutService', BASICS_SCOPE_ENTITY_LAYOUT_GENERATOR, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/material');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? new importedModule.BasicsMaterialScopeLayoutService() : null;
		
	}),
LazyInjectableInfo.create('basics.material.BasicsScopeValidationServiceFactory', BASICS_SCOPE_VALIDATION_SERVICE_FACTORY, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/material');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.BasicsScopeValidationServiceFactory) : null;
		
	}),
LazyInjectableInfo.create('basics.material.BasicsMaterialScopeDetailLayoutService', BASICS_SCOPE_DETAIL_ENTITY_LAYOUT_GENERATOR, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/material');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? new importedModule.BasicsMaterialScopeDetailLayoutService() : null;
		
	}),
LazyInjectableInfo.create('basics.material.BasicsScopeDetailValidationServiceFactory', BASICS_SCOPE_DETAIL_VALIDATION_SERVICE_FACTORY, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/material');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.BasicsScopeDetailValidationServiceFactory) : null;
		
	}),
LazyInjectableInfo.create('basics.procurementconfiguration.BasicsProcurementConfigLookupLayoutService', BASICS_PRC_CONFIG_LOOKUP_LAYOUT_GENERATOR, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/procurementconfiguration');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.BasicsProcurementConfigLookupLayoutService) : null;
		
	}),
LazyInjectableInfo.create('basics.site.BasicsSiteLookupProviderService', BASICS_SITE_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/site');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.BasicsSiteLookupProviderService) : null;
		
	}),
LazyInjectableInfo.create('basics.customize.BasicsCustomizeLookupProvider', BASICS_CUSTOMIZE_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/customize');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.BasicsCustomizeLookupProvider) : null;
		
	}),
LazyInjectableInfo.create('basics.efbsheets.BasicsEfbsheetsLookupProviderService', BASICS_EFBSHEETS_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/efbsheets');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.BasicsEfbsheetsLookupProviderService) : null;
		
	}),

	LazyInjectableInfo.create('basics.characteristic.BasicsCharacteristicDiscreteValueDataProviderService', BASICS_CHARACTERISTIC_DISCRETE_VALUE_DATA_PROVIDER, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/basics/characteristic');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.BasicsCharacteristicDiscreteValueDataProviderService) : null;
		
	}),
];
 