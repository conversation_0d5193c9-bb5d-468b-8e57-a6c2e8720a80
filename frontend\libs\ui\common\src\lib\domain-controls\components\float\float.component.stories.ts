/*
 * Copyright(c) RIB Software GmbH
 */

import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { FloatComponent } from './float.component';
import { ControlContextInjectionToken } from '../../model/control-context.interface';
import { FloatConfigInjectionToken, IFloatConfig } from '../../model/float-config.interface';
import { INumericControlContext } from '../../model/numeric-control-context.interface';
import { UiNumericConverterService } from '../../services/numeric-converter.service';
import { DebounceChangeDirective } from '../../../directives/ng-model-debounce.directive';
import { MinimalEntityContext, IEntityContext, PlatformConfigurationService, TranslatePipe, PlatformTranslateService, PlatformLanguageService } from '@libs/platform/common';
import { HttpClientModule } from '@angular/common/http';

// Create a mock control context
const createMockControlContext = (args: Partial<INumericControlContext>): INumericControlContext => ({
	fieldId: 'floatValue',
	readonly: false,
	validationResults: [],
	value: 0.00,
	minValue: undefined,
	maxValue: undefined,
	placeholder: 'Enter decimal number...',
	get entityContext(): IEntityContext<object> {
		return new MinimalEntityContext<object>();
	},
	...args
});

// Create a mock float config
const createMockFloatConfig = (decimalPlaces: number): IFloatConfig => ({
	decimalPlaces
});

export default {
	title: 'UI Common/Domain Controls/FloatComponent',
	component: FloatComponent,
	parameters: {
		docs: {
			description: {
				component: `
A floating-point number input component that provides:
- Decimal number input with configurable precision
- Configurable min/max value constraints
- Customizable placeholder text
- Readonly mode support
- Automatic decimal formatting
- Locale-aware number formatting

**Key Features:**
- Supports configurable decimal places (precision)
- Restricts input to valid decimal numbers
- Supports minValue and maxValue for validation
- Locale-aware number formatting
- Integrates with Angular reactive forms
- Built-in numeric conversion and validation
- Handles large numbers and exponential notation
			`,
			},
		},
	},
	decorators: [
		moduleMetadata({
			imports: [FormsModule, DebounceChangeDirective, HttpClientModule,TranslatePipe],
			declarations: [],
			providers: [
				UiNumericConverterService,
				PlatformConfigurationService,
				PlatformTranslateService,
				PlatformLanguageService,
				
			],
		}),
	],
	argTypes: {
		minValue: {
			control: { type: 'number', step: 0.01 },
			description: 'Minimum allowable decimal value',
		},
		maxValue: {
			control: { type: 'number', step: 0.01 },
			description: 'Maximum allowable decimal value',
		},
		placeholder: {
			control: { type: 'text' },
			description: 'Placeholder text shown when input is empty',
		},
		value: {
			control: { type: 'number', step: 0.01 },
			description: 'Current decimal value',
		},
		readonly: {
			control: { type: 'boolean' },
			description: 'Whether the input is readonly',
		},
		decimalPlaces: {
			control: { type: 'number', min: 0, max: 10, step: 1 },
			description: 'Number of decimal places to display',
		},
	},
	render: (args: { 
		minValue?: number; 
		maxValue?: number; 
		placeholder?: string; 
		value?: number; 
		readonly?: boolean;
		decimalPlaces?: number;
	}) => {
		const controlContext = createMockControlContext({
			minValue: args.minValue,
			maxValue: args.maxValue,
			placeholder: args.placeholder,
			value: args.value,
			readonly: args.readonly,
		});

		const floatConfig = createMockFloatConfig(args.decimalPlaces ?? 2);

		return {
			props: {},
			moduleMetadata: {
				providers: [
					{ provide: ControlContextInjectionToken, useValue: controlContext },
					{ provide: FloatConfigInjectionToken, useValue: floatConfig }
				]
			}
		};
	},
} as Meta<FloatComponent & {
	minValue?: number;
	maxValue?: number;
	placeholder?: string;
	value?: number;
	readonly?: boolean;
	decimalPlaces?: number;
}>;

type Story = StoryObj<FloatComponent & {
	minValue?: number;
	maxValue?: number;
	placeholder?: string;
	value?: number;
	readonly?: boolean;
	decimalPlaces?: number;
}>;

export const Default: Story = {
	args: {
		minValue: undefined,
		maxValue: undefined,
		placeholder: 'Enter decimal number...',
		
		readonly: false,
		decimalPlaces: 2,
	},
};

