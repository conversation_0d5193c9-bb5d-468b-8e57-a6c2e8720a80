import { <PERSON><PERSON>, StoryObj, moduleMetadata } from '@storybook/angular';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { CheckBoxComponent } from './check-box.component';
import { ControlContextInjectionToken, IControlContext } from '../../model/control-context.interface';
import { MinimalEntityContext, IEntityContext } from '@libs/platform/common';

// Create a mock control context
const createMockControlContext = (args: Partial<IControlContext<boolean>>): IControlContext<boolean> => ({
	fieldId: 'checkbox',
	readonly: false,
	validationResults: [],
	value: false,
	get entityContext(): IEntityContext<object> {
		return new MinimalEntityContext<object>();
	},
	...args
});

export default {
	title: 'UI Common/Domain Controls/CheckBoxComponent',
	component: CheckBoxComponent,
	parameters: {
		docs: {
			description: {
				component: `
A checkbox input component that provides:
- Boolean value selection (checked/unchecked)
- Readonly mode support
- Built-in validation support
- Accessible form integration

**Key Features:**
- Simple boolean state management
- Support for readonly/disabled state
- Integrates with Angular reactive forms
- Validation result display support
- Accessible checkbox implementation
			`,
			},
		},
	},
	decorators: [
		moduleMetadata({
			imports: [CommonModule, FormsModule],
			providers: []
		}),
	],
	argTypes: {
		value: {
			control: { type: 'boolean' },
			description: 'Whether the checkbox is checked',
		},
		readonly: {
			control: { type: 'boolean' },
			description: 'Whether the checkbox is readonly/disabled',
		},
		fieldId: {
			control: { type: 'text' },
			description: 'Unique field identifier',
		},
	},
	render: (args: { 
		value?: boolean; 
		readonly?: boolean;
		fieldId?: string;
	}) => {
		const controlContext = createMockControlContext({
			value: args.value,
			readonly: args.readonly,
			fieldId: args.fieldId || 'checkbox',
		});

		return {
			props: {},
			moduleMetadata: {
				providers: [
					{ provide: ControlContextInjectionToken, useValue: controlContext }
				]
			}
		};
	},
} as Meta<CheckBoxComponent & {
	value?: boolean;
	readonly?: boolean;
	fieldId?: string;
}>;

type Story = StoryObj<CheckBoxComponent & {
	value?: boolean;
	readonly?: boolean;
	fieldId?: string;
}>;

export const Default: Story = {
	args: {
		value: false,
		readonly: false,
		fieldId: 'checkbox',
	},
};
