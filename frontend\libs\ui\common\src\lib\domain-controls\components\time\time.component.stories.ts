import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { TimeComponent } from './time.component';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TimeConfigInjectionToken } from '../../model/time-config.interface';
import { ControlContextInjectionToken } from '../../model/control-context.interface';
import { FieldType } from '../../../model/fields';

// Helper function to create a Date from time string
const createTimeValue = (timeString: string): Date => {
  const date = new Date();
  if (timeString) {
    const [hours, minutes] = timeString.split(':').map(Number);
    date.setHours(hours, minutes, 0, 0);
  }
  return date;
};

// Define the story arguments type
type StoryArgs = {
  fieldId?: string;
  value?: string;
  timeFormat?: string;
  placeholder?: string;
  readonly?: boolean;
  maxLength?: number;
  timeType?: 'Local' | 'UTC';
};

const meta: Meta<StoryArgs> = {
  title: 'UI Common/Domain Controls/Time',
  component: TimeComponent,
  parameters: {
    docs: {
      description: {
        component: `
A time input component that supports both local and UTC time formats.

**Key Features:**
- Local and UTC time support
- Customizable time format (HH:mm, HH:mm:ss)
- Readonly state support
- Input validation
- Placeholder text support

**Time Types:**
- **Local Time**: Time in the user's local timezone
- **UTC Time**: Time in UTC timezone

**Supported Formats:**
- HH:mm (24-hour format, default)
- HH:mm:ss (24-hour format with seconds)
        `,
      },
    },
  },
  argTypes: {
    // Field Configuration
    fieldId: {
      control: { type: 'text' },
      description: 'Unique identifier for the time field',
    },
    value: {
      control: { type: 'text' },
      description: 'Initial time value (HH:mm format, e.g., "14:30")',
    },
    timeFormat: {
      control: { type: 'select' },
      options: ['HH:mm', 'HH:mm:ss'],
      description: 'Time display format',
    },
    placeholder: {
      control: { type: 'text' },
      description: 'Placeholder text for the input field',
    },
    maxLength: {
      control: { type: 'number', min: 1, max: 20 },
      description: 'Maximum length of the input',
    },

    // State Configuration
    readonly: {
      control: { type: 'boolean' },
      description: 'Whether the input is readonly',
    },
    timeType: {
      control: { type: 'select' },
      options: ['Local', 'UTC'],
      description: 'Time type - Local or UTC',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [CommonModule, FormsModule],
    }),
  ],
  render: (args: StoryArgs) => {
    // Create dynamic control context based on args
    const controlContext = {
      fieldId: args.fieldId || 'timeField',
      value: args.value ? createTimeValue(args.value) : undefined,
      maxLength: args.maxLength || 5,
      placeholder: args.placeholder || 'Select time',
      readonly: args.readonly ?? false,
      validationResults: [],
      entityContext: { totalCount: 0 },
      options: { format: args.timeFormat || 'HH:mm' }
    };

    // Create dynamic time config based on args
    const timeConfig = {
      type: args.timeType === 'UTC' ? FieldType.TimeUtc : FieldType.Time
    };

    return {
      template: '<ui-common-time></ui-common-time>',
      moduleMetadata: {
        providers: [
          { provide: ControlContextInjectionToken, useValue: controlContext },
          { provide: TimeConfigInjectionToken, useValue: timeConfig }
        ]
      }
    };
  },
};

export default meta;

type Story = StoryObj<StoryArgs>;

export const Default: Story = {
  args: {
    fieldId: 'appointmentTime',
    value: '08:30',
    timeFormat: 'HH:mm',
    placeholder: 'Select time',
    readonly: false,
    maxLength: 5,
    timeType: 'Local',
  },
};

