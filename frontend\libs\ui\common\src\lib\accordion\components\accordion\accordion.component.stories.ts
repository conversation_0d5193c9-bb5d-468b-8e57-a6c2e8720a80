
import { <PERSON>a, StoryObj, moduleMetadata } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { MatExpansionModule } from '@angular/material/expansion';
import { UiCommonAccordionComponent } from './accordion.component';
import { UiCommonAccordionTreeComponent } from '../accordion-tree/accordion-tree.component';
import { UiCommonAccordionItemComponent } from '../accordion-item/accordion-item.component';
import { IAccordionItem } from '../../model/interfaces/accordion-item.interface';
import { IComponentInputs } from '../../../model/components/component-inputs.interface';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { UiCommonAccordionItemContentComponent } from '../accordion-item-content/accordion-item-content.component';
import { PlatformConfigurationService, PlatformTranslateService, TranslatePipe } from '@libs/platform/common';
import { HttpClientModule } from '@angular/common/http';

const dummyAccordionData: IAccordionItem<IComponentInputs>[] = [
  {
    id: 69,
    title: { text: 'Overview' },
    expanded: true,
    actionButtons: [],
     providers: [], 
    children: [
      {
        id: 182,
        title: { text: 'Project Overview' },
        comment: { text: 'groupable overview of all projects' },
        actionButtons: [],
         providers: [], 
      },
      {
        id: 207,
        title: { text: 'Project Involved in project Overview' },
        comment: { text: 'List of participants' },
        actionButtons: [],
         providers: [], 
      },
      {
        id: 183,
        title: { text: 'Project Detail' },
        comment: { text: 'Detailed information on the project' },
        actionButtons: [],
         providers: [], 
      }
    ]
  },
  {
    id: 70,
    title: { text: 'Analysis' },
    expanded: true,
    actionButtons: [],
     providers: [], 
    children: [
      {
        id: 182,
        title: { text: 'Project Tender Results' },
        comment: { text: 'Tender results per project' },
        actionButtons: [],
         providers: [], 
      },
      {
        id: 207,
        title: { text: 'Project Estimate Statistic by Manager' },
        comment: { text: 'Statistic per manager' },
        actionButtons: [],
         providers: [], 
      },
      {
        id: 183,
        title: { text: 'Project Sales Comparison' },
        comment: { text: 'Comparison amounts Bid / Contract / WIP' },
        actionButtons: [],
         providers: [], 
      }
    ]
  },
  
];

// Accordion config
const mockConfig =  {
  multi: true,
  actionButtonDirection: 'row',
  itemProviders: [], 
  actionButtons: [] 
};

export default {
  title: 'UI Common/Accordion/Accordion Component',
  component: UiCommonAccordionComponent,
  decorators: [
    moduleMetadata({
      imports: [CommonModule, MatExpansionModule,BrowserAnimationsModule,HttpClientModule,TranslatePipe],
      declarations: [
         UiCommonAccordionComponent,
          UiCommonAccordionTreeComponent,
          UiCommonAccordionItemComponent,
          
          UiCommonAccordionItemContentComponent,
        
        
      ],
       providers: [PlatformTranslateService,PlatformConfigurationService],
    }),
  ],
  render: (args) => ({
    props: {
      ...args,
    },
  }),
} as Meta<UiCommonAccordionComponent<IComponentInputs>>;

type Story = StoryObj<UiCommonAccordionComponent<IComponentInputs>>;

export const Default: Story = {
  args: {
    data: dummyAccordionData,
    options: mockConfig,
  },
};