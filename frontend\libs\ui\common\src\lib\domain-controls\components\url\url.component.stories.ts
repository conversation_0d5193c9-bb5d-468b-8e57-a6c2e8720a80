import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { ReactiveFormsModule, FormControl, Validators } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { UrlComponent } from './url.component';
import { UrlScheme } from '../../model/enums/url-scheme.enum';
import { ControlContextInjectionToken, IControlContext } from '../../model/control-context.interface';
import { IEntityContext, MinimalEntityContext, PlatformConfigurationService, PlatformTranslateService, TranslatePipe } from '@libs/platform/common';

// Define the story arguments type
type StoryArgs = {
  value?: string;
  readonly?: boolean;
  placeholder?: string;
  defaultScheme?: UrlScheme;
  error?: boolean;
};

const ctlCtx: IControlContext = {
  fieldId: 'SingleLineText',
  readonly: false,
  validationResults: [],
  get entityContext(): IEntityContext<object> {
    return new MinimalEntityContext();
  },
};

export default {
  title: 'UI Common/Domain Controls/UrlComponent',
  component: UrlComponent,
  decorators: [
    moduleMetadata({
      imports: [ReactiveFormsModule, HttpClientModule,TranslatePipe],
      declarations: [],
      providers: [
        PlatformTranslateService,
        PlatformConfigurationService,
        { provide: ControlContextInjectionToken, useValue: ctlCtx },
      ],
    }),
  ],
  argTypes: {
    value: {
      control: 'text',
      description: 'URL value',
      defaultValue: '',
    },
    readonly: {
      control: 'boolean',
      description: 'Is input readonly',
      defaultValue: false,
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text',
      defaultValue: 'https://www.example.com',
    },
    defaultScheme: {
      control: 'select',
      options: [
        UrlScheme.Http,
        UrlScheme.Https,
        UrlScheme.Ftp,
        UrlScheme.Ftps,
        UrlScheme.File,
        undefined,
      ],
      description: 'Default URL scheme',
      defaultValue: undefined,
    },
    error: {
      control: 'boolean',
      description: 'Force error state',
      defaultValue: false,
    },
  },
  parameters: {
    docs: {
      description: {
        component: 'A URL input box with validation, protocol selection, and open-in-new-tab support.',
      },
    },
  },
} as Meta<StoryArgs>;

type Story = StoryObj<StoryArgs>;

const makeProps = (args: StoryArgs) => {
  const urlString = new FormControl(
    args.value,
    args.error
      ? [Validators.required, () => ({ urlInvalid: true })]
      : [Validators.required]
  );
  return {
    controlContext: {
      ...ctlCtx,
      value: args.value,
      readonly: args.readonly,
      placeholder: args.placeholder,
    },
    urlString,
    defaultScheme: args.defaultScheme,
    urlExample: args.placeholder,
  };
};

export const Playground: Story = {
  args: {
    value: '',
    readonly: false,
    placeholder: 'https://www.example.com',
    defaultScheme: undefined,
    error: false,
  },
  render: (args) => ({
    props: makeProps(args),
  }),
};

