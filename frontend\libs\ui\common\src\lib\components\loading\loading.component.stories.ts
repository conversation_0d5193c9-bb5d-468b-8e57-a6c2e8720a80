import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { UiCommonLoadingComponent } from './loading.component';
import { PlatformConfigurationService, PlatformTranslateService, TranslatePipe } from '@libs/platform/common';
import { HttpClientModule } from '@angular/common/http';

export default {
  title: 'UI Common/Loading/Loading Component',
  component: UiCommonLoadingComponent,
  decorators: [
    moduleMetadata({
      imports: [CommonModule,HttpClientModule, TranslatePipe],
      declarations: [UiCommonLoadingComponent],
      providers: [
        PlatformTranslateService,
        PlatformConfigurationService
      ]
    }),
  ],
  render: (args) => ({
    props: {
      ...args,
    },
  }),
} as Meta<UiCommonLoadingComponent>;

type Story = StoryObj<UiCommonLoadingComponent>;

export const Default: Story = {
  args: {
    loading: true,
    info: { text: 'Loading, please wait...' },
    backdrop: false,
    cssClass: '',
  },
};
