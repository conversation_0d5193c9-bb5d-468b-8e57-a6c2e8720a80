import { <PERSON>a, StoryObj, moduleMetadata } from '@storybook/angular';
import { SelectComponent } from './select.component';
import { ISelectControlContext } from '../../model/select-control-context.interface';
import { ControlContextInjectionToken } from '../../model/control-context.interface';
import { FormsModule } from '@angular/forms';
// import { ISelectItem, ISelectItemsSource } from '@libs/ui/common';
import { PlatformConfigurationService, PlatformTranslateService, TranslatePipe, MinimalEntityContext, IEntityContext } from '@libs/platform/common';
import { HttpClientModule } from '@angular/common/http';
import { ISelectItem, ISelectItemsSource } from '../../../model/fields';
// import { ISelectItem, ISelectItemsSource } from '../../model/fields/additional/select-items-source.model';

// Create a mock control context
const createMockControlContext = (args: { selectedValue?: string | number; readonly?: boolean; fieldId?: string; optionType?: string }): ISelectControlContext => {
	let items: ISelectItem<string | number>[] = [];

	switch (args.optionType) {
		case 'colors':
			items = [
				{ id: 'red', displayName: 'Red', iconCSS: 'icon-red' },
				{ id: 'green', displayName: 'Green', iconCSS: 'icon-green' },
				{ id: 'blue', displayName: 'Blue', iconCSS: 'icon-blue' },
			];
			break;
		case 'sizes':
			items = [
				{ id: 'small', displayName: 'Small', iconCSS: 'icon-small' },
				{ id: 'medium', displayName: 'Medium', iconCSS: 'icon-medium' },
				{ id: 'large', displayName: 'Large', iconCSS: 'icon-large' },
			];
			break;
		default: // 'users'
			items = [
				{ id: 1, displayName: 'Administrator', iconCSS: 'icon-admin' },
				{ id: 2, displayName: 'User', iconCSS: 'icon-user' },
				{ id: 3, displayName: 'System', iconCSS: 'icon-guest' },
			];
	}

	const itemsSource: ISelectItemsSource<string | number> = { items };

	return {
		fieldId: args.fieldId || 'selectField',
		readonly: args.readonly || false,
		validationResults: [],
		value: args.selectedValue,
		itemsSource: itemsSource,
		get entityContext(): IEntityContext<object> {
			return new MinimalEntityContext<object>();
		},
	};
};

export default {
	title: 'UI Common/Domain Controls/SelectComponent',
	component: SelectComponent,
	parameters: {
		docs: {
			description: {
				component: `
A dropdown select component that provides:
- Single selection from multiple options
- Configurable select items with display names and icons
- Readonly mode support
- Built-in validation support
- Translatable option labels

`,
			},
		},
	},
	decorators: [
		moduleMetadata({
			imports: [FormsModule, HttpClientModule,TranslatePipe],
			declarations: [],
			providers: [PlatformTranslateService, PlatformConfigurationService],
		}),
	],
	argTypes: {
		selectedValue: {
			control: { type: 'select' },
			options: [undefined, 1, 2, 3],
			description: 'Currently selected option value',
		},
		readonly: {
			control: { type: 'boolean' },
			description: 'Whether the select dropdown is readonly/disabled',
		},
		fieldId: {
			control: { type: 'text' },
			description: 'Unique field identifier for the select element',
		},
		optionType: {
			control: { type: 'select' },
			options: ['users', 'colors', 'sizes'],
			description: 'Type of options to display in the dropdown',
		},
	},
	render: (args: { selectedValue?: string | number; readonly?: boolean; fieldId?: string; optionType?: string }) => {
		const controlContext = createMockControlContext({
			selectedValue: args.selectedValue,
			readonly: args.readonly,
			fieldId: args.fieldId,
			optionType: args.optionType,
		});

		return {
			props: {},
			moduleMetadata: {
				providers: [{ provide: ControlContextInjectionToken, useValue: controlContext }],
			},
		};
	},
} as Meta<
	SelectComponent & {
		selectedValue?: string | number;
		readonly?: boolean;
		fieldId?: string;
		optionType?: string;
	}
>;

type Story = StoryObj<
	SelectComponent & {
		selectedValue?: string | number;
		readonly?: boolean;
		fieldId?: string;
		optionType?: string;
	}
>;

export const Default: Story = {
	args: {
		selectedValue: 1,
		readonly: false,
		fieldId: 'selectField',
		optionType: 'users',
	},
};
