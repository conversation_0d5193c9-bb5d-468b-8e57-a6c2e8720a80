/*
 * Copyright(c) RIB Software GmbH
 */

import { BasicsSharedBillingSchemaEntityInfo } from '@libs/basics/shared';
import { ProcurementContractBillingSchemaDataService } from '../../services/procurement-contract-billing-schema-data.service';
import { ProcurementContractBillingSchemaBehavior } from '../../behaviors/procurement-contract-billing-schema-behavior.service';

export const PROCUREMENT_CONTRACT_BILLING_SCHEMA_ENTITY_INFO = BasicsSharedBillingSchemaEntityInfo.create({
	permissionUuid: '9f5d33b39555424ba877447f2bfd1269',
	dataServiceToken: ProcurementContractBillingSchemaDataService,
	behavior: ProcurementContractBillingSchemaBehavior,
	projectFkGetter: mainEntity => mainEntity.ProjectFk,
});